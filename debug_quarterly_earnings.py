# -*- coding: utf-8 -*-
"""
调试 quarterly earnings 搜索问题
"""

from database import IndexDatabase
from tokenizer import EconomicsTokenizer
from app import SearchEngine
import requests


def check_quarterly_earnings_in_database():
    """检查数据库中是否有quarterly earnings相关的字段"""
    print("=== 检查数据库中的quarterly earnings相关字段 ===")
    
    db = IndexDatabase()
    
    with db.get_connection() as conn:
        # 查找包含quarterly的字段
        print("1. 查找包含'quarterly'的字段:")
        cursor = conn.execute('''
            SELECT field_id, description, category_name, file_source
            FROM fields 
            WHERE field_id LIKE '%quarterly%' OR description LIKE '%quarterly%'
            ORDER BY field_id
            LIMIT 20
        ''')
        
        quarterly_fields = cursor.fetchall()
        print(f"   找到 {len(quarterly_fields)} 个包含'quarterly'的字段:")
        
        for field_id, description, category_name, file_source in quarterly_fields:
            print(f"   - {field_id}: {description[:60]}... ({category_name}, {file_source})")
        
        # 查找包含earnings的字段
        print(f"\n2. 查找包含'earnings'的字段:")
        cursor = conn.execute('''
            SELECT field_id, description, category_name, file_source
            FROM fields 
            WHERE field_id LIKE '%earnings%' OR description LIKE '%earnings%'
            ORDER BY field_id
            LIMIT 20
        ''')
        
        earnings_fields = cursor.fetchall()
        print(f"   找到 {len(earnings_fields)} 个包含'earnings'的字段:")
        
        for field_id, description, category_name, file_source in earnings_fields:
            print(f"   - {field_id}: {description[:60]}... ({category_name}, {file_source})")
        
        # 查找同时包含quarterly和earnings的字段
        print(f"\n3. 查找同时包含'quarterly'和'earnings'的字段:")
        cursor = conn.execute('''
            SELECT field_id, description, category_name, file_source
            FROM fields 
            WHERE (field_id LIKE '%quarterly%' AND field_id LIKE '%earnings%') 
               OR (description LIKE '%quarterly%' AND description LIKE '%earnings%')
            ORDER BY field_id
        ''')
        
        both_fields = cursor.fetchall()
        print(f"   找到 {len(both_fields)} 个同时包含两个词的字段:")
        
        for field_id, description, category_name, file_source in both_fields:
            print(f"   - {field_id}: {description[:60]}... ({category_name}, {file_source})")


def test_tokenization():
    """测试quarterly earnings的分词"""
    print("\n=== 测试quarterly earnings分词 ===")
    
    tokenizer = EconomicsTokenizer()
    
    query = "quarterly earnings"
    tokens = tokenizer.tokenize_description(query)
    
    print(f"查询: '{query}'")
    print(f"分词结果: {tokens}")
    
    # 测试单独的词
    for word in ["quarterly", "earnings"]:
        tokens = tokenizer.tokenize_description(word)
        print(f"'{word}' 分词: {tokens}")


def test_word_index():
    """测试词汇索引中的情况"""
    print("\n=== 测试词汇索引 ===")
    
    db = IndexDatabase()
    
    with db.get_connection() as conn:
        # 查找quarterly在索引中的情况
        print("1. 查找'quarterly'在词汇索引中:")
        cursor = conn.execute('''
            SELECT word, word_type, field_id, source_text, COUNT(*) as count
            FROM word_index 
            WHERE word LIKE '%quarterly%'
            GROUP BY word, word_type, field_id
            ORDER BY word, field_id
            LIMIT 20
        ''')
        
        quarterly_indexes = cursor.fetchall()
        print(f"   找到 {len(quarterly_indexes)} 个'quarterly'相关索引:")
        
        for word, word_type, field_id, source_text, count in quarterly_indexes:
            print(f"   - 词汇:'{word}' 类型:{word_type} 字段:{field_id} 原文:{source_text}")
        
        # 查找earnings在索引中的情况
        print(f"\n2. 查找'earnings'在词汇索引中:")
        cursor = conn.execute('''
            SELECT word, word_type, field_id, source_text, COUNT(*) as count
            FROM word_index 
            WHERE word LIKE '%earnings%'
            GROUP BY word, word_type, field_id
            ORDER BY word, field_id
            LIMIT 20
        ''')
        
        earnings_indexes = cursor.fetchall()
        print(f"   找到 {len(earnings_indexes)} 个'earnings'相关索引:")
        
        for word, word_type, field_id, source_text, count in earnings_indexes:
            print(f"   - 词汇:'{word}' 类型:{word_type} 字段:{field_id} 原文:{source_text}")


def test_individual_word_search():
    """测试单独搜索每个词"""
    print("\n=== 测试单独搜索每个词 ===")
    
    search_engine = SearchEngine()
    
    # 搜索quarterly
    print("1. 搜索'quarterly':")
    quarterly_results = search_engine.search("quarterly")
    print(f"   找到 {len(quarterly_results)} 个结果")
    
    if quarterly_results:
        print("   前5个结果:")
        for i, result in enumerate(quarterly_results[:5], 1):
            print(f"   {i}. {result['field_id']} - {result['description'][:50]}...")
    
    # 搜索earnings
    print(f"\n2. 搜索'earnings':")
    earnings_results = search_engine.search("earnings")
    print(f"   找到 {len(earnings_results)} 个结果")
    
    if earnings_results:
        print("   前5个结果:")
        for i, result in enumerate(earnings_results[:5], 1):
            print(f"   {i}. {result['field_id']} - {result['description'][:50]}...")


def test_combined_search():
    """测试组合搜索quarterly earnings"""
    print("\n=== 测试组合搜索quarterly earnings ===")
    
    search_engine = SearchEngine()
    
    # 搜索quarterly earnings
    print("搜索'quarterly earnings':")
    results = search_engine.search("quarterly earnings")
    print(f"找到 {len(results)} 个结果")
    
    if results:
        print("\n前10个结果:")
        for i, result in enumerate(results[:10], 1):
            id_matches = result.get('id_matches', 0)
            desc_matches = result.get('desc_matches', 0)
            match_count = result.get('match_count', 0)
            matched_words = result.get('matched_words', [])
            
            print(f"{i:2d}. {result['field_id']}")
            print(f"     描述: {result['description'][:60]}...")
            print(f"     类别: {result['category_name']}")
            print(f"     匹配: ID={id_matches}, 描述={desc_matches}, 总计={match_count}")
            print(f"     匹配词汇: {matched_words}")
            print()
    else:
        print("   没有找到结果！")


def test_similar_terms():
    """测试相似术语"""
    print("\n=== 测试相似术语 ===")
    
    search_engine = SearchEngine()
    
    similar_terms = [
        "quarter",
        "earning", 
        "profit",
        "income",
        "revenue"
    ]
    
    for term in similar_terms:
        print(f"\n搜索'{term}':")
        results = search_engine.search(term)
        print(f"   找到 {len(results)} 个结果")
        
        if results:
            # 显示前3个结果
            for i, result in enumerate(results[:3], 1):
                print(f"   {i}. {result['field_id']} - {result['description'][:40]}...")


def test_web_api():
    """测试Web API的搜索结果"""
    print("\n=== 测试Web API ===")
    
    try:
        response = requests.get("http://127.0.0.1:5000/api/search?q=quarterly%20earnings", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"API搜索'quarterly earnings':")
            print(f"   返回 {data['total_results']} 个结果")
            
            if data['results']:
                print("\n   前5个结果:")
                for i, result in enumerate(data['results'][:5], 1):
                    print(f"   {i}. {result['field_id']} - {result['description'][:50]}...")
            else:
                print("   API也没有返回结果")
        
        else:
            print(f"API请求失败: {response.status_code}")
    
    except Exception as e:
        print(f"无法测试API: {e}")


def suggest_alternative_searches():
    """建议替代搜索词"""
    print("\n=== 建议替代搜索词 ===")
    
    search_engine = SearchEngine()
    
    alternatives = [
        "quarter",
        "earning", 
        "eps",  # earnings per share
        "net income",
        "profit",
        "revenue",
        "financial",
        "income statement"
    ]
    
    print("如果'quarterly earnings'没有结果，可以尝试这些替代词:")
    
    for alt in alternatives:
        results = search_engine.search(alt)
        if results:
            print(f"   '{alt}': {len(results)} 个结果")
            # 显示一个最相关的结果
            best_result = results[0]
            print(f"      最佳匹配: {best_result['field_id']} - {best_result['description'][:40]}...")
        else:
            print(f"   '{alt}': 0 个结果")


if __name__ == "__main__":
    print("开始调试 quarterly earnings 搜索问题...")
    
    # 1. 检查数据库中是否有相关字段
    check_quarterly_earnings_in_database()
    
    # 2. 测试分词
    test_tokenization()
    
    # 3. 测试词汇索引
    test_word_index()
    
    # 4. 测试单独搜索
    test_individual_word_search()
    
    # 5. 测试组合搜索
    test_combined_search()
    
    # 6. 测试相似术语
    test_similar_terms()
    
    # 7. 测试Web API
    test_web_api()
    
    # 8. 建议替代搜索
    suggest_alternative_searches()
    
    print("\n调试完成！")
