# -*- coding: utf-8 -*-
"""
经济学数据字段搜索引擎配置文件
"""

import os

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 数据文件配置
DATA_CONFIG = {
    'csv_folder': os.path.join(PROJECT_ROOT, 'split_files'),
    'index_db_path': os.path.join(PROJECT_ROOT, 'data', 'field_index.db'),
    'economics_dict_path': os.path.join(PROJECT_ROOT, 'data', 'economics_dict.txt')
}

# 分词配置
TOKENIZER_CONFIG = {
    'jieba_dict_path': os.path.join(PROJECT_ROOT, 'data', 'economics_dict.txt'),
    'enable_paddle': False,  # 是否启用paddle模式
    'hmm': True,  # 是否使用HMM模型
    'cut_all': False  # 是否全模式分词
}

# 数据库配置
DATABASE_CONFIG = {
    'index_db': os.path.join(PROJECT_ROOT, 'data', 'field_index.db'),
    'create_tables_on_startup': True
}

# 搜索配置
SEARCH_CONFIG = {
    'min_query_length': 1,
    'max_results': 100,
    'fuzzy_threshold': 0.6,  # 模糊匹配阈值
    'enable_category_filter': True
}

# 数据类别映射
CATEGORY_MAPPING = {
    'pv': 'Price Volume',
    'fundamental': 'Fundamental', 
    'model': 'Model',
    'news': 'News',
    'sentiment': 'Sentiment',
    'analyst': 'Analyst'
}

# 地区映射
REGION_MAPPING = {
    'CHN': '中国',
    'USA': '美国', 
    'EUR': '欧洲',
    'GLB': '全球',
    'ASI': '亚洲'
}

# 经济学专业词汇（初始版本，后续可扩展）
ECONOMICS_TERMS = [
    # 基本面相关
    '市值', '市盈率', '市净率', '净资产收益率', '资产负债率', '流动比率',
    '总资产', '净利润', '营业收入', '现金流', '股东权益', '负债',
    'market cap', 'P/E ratio', 'P/B ratio', 'ROE', 'debt ratio', 'current ratio',
    'total assets', 'net profit', 'revenue', 'cash flow', 'equity', 'liability',
    
    # 技术分析相关  
    '移动平均', '成交量', '换手率', '振幅', '涨跌幅', '开盘价', '收盘价',
    'moving average', 'volume', 'turnover', 'amplitude', 'change', 'open', 'close',
    
    # 风险指标
    '贝塔', '阿尔法', '夏普比率', '波动率', '最大回撤', '风险价值',
    'beta', 'alpha', 'sharpe ratio', 'volatility', 'max drawdown', 'VaR',
    
    # 行业分类
    '金融', '科技', '医疗', '消费', '工业', '能源', '房地产', '公用事业',
    'finance', 'technology', 'healthcare', 'consumer', 'industrial', 'energy', 'real estate', 'utilities'
]

# 确保数据目录存在
os.makedirs(os.path.dirname(DATA_CONFIG['index_db_path']), exist_ok=True)
