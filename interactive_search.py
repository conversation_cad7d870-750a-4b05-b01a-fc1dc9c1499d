#!/usr/bin/env python3
"""
交互式数据字段搜索界面
提供命令行交互式搜索体验
"""

import sys
import argparse
from field_searcher import FieldSearcher, SearchResult
from typing import List, Optional

class InteractiveSearcher:
    """交互式搜索器"""
    
    def __init__(self, data_dir: str = "split_files"):
        """初始化交互式搜索器"""
        print("正在初始化数据字段检索器...")
        self.searcher = FieldSearcher(data_dir)
        self.current_results = []
        
    def show_help(self):
        """显示帮助信息"""
        help_text = """
数据字段检索器 - 交互式搜索界面

可用命令:
  search <查询词>          - 执行搜索
  method <方法名>          - 设置相似度方法 (cosine/fuzzy/keyword/hybrid)
  top <数量>              - 设置返回结果数量
  filter region <地区>     - 按地区过滤
  filter type <类型>       - 按类型过滤
  filter clear            - 清除所有过滤器
  show <索引>             - 显示详细结果
  stats                   - 显示数据统计
  export <文件名>          - 导出当前结果到CSV
  help                    - 显示此帮助
  quit/exit               - 退出程序

相似度方法说明:
  cosine   - 余弦相似度 (基于TF-IDF向量)
  fuzzy    - 模糊匹配 (基于编辑距离)
  keyword  - 关键词匹配 (基于Jaccard相似度)
  hybrid   - 混合方法 (推荐，综合多种算法)

示例:
  search market cap
  method hybrid
  top 20
  filter region CHN
  show 1
        """
        print(help_text)
    
    def show_stats(self):
        """显示数据统计"""
        stats = self.searcher.get_statistics()
        
        print(f"\n{'='*50}")
        print("数据统计信息")
        print(f"{'='*50}")
        print(f"总记录数: {stats['total_records']:,}")
        print(f"唯一地区数: {stats['unique_regions']}")
        print(f"唯一类型数: {stats['unique_types']}")
        print(f"唯一分类数: {stats['unique_categories']}")
        
        print(f"\n地区分布:")
        for region, count in stats['regions'].items():
            print(f"  {region}: {count:,}")
        
        print(f"\n类型分布:")
        for type_name, count in stats['types'].items():
            print(f"  {type_name}: {count:,}")
        
        print(f"\n主要分类 (前10):")
        for category, count in stats['categories'].items():
            print(f"  {category}: {count:,}")
    
    def export_results(self, filename: str):
        """导出搜索结果到CSV"""
        if not self.current_results:
            print("没有可导出的搜索结果")
            return
        
        import pandas as pd
        
        # 转换结果为DataFrame
        data = []
        for result in self.current_results:
            data.append({
                'ID': result.id,
                'Description': result.description,
                'Similarity_Score': result.similarity_score,
                'Region': result.region,
                'Universe': result.universe,
                'Type': result.type,
                'Category': result.category,
                'Subcategory': result.subcategory,
                'Dataset_Name': result.dataset_name,
                'Coverage': result.coverage,
                'User_Count': result.user_count,
                'Alpha_Count': result.alpha_count
            })
        
        df = pd.DataFrame(data)
        
        # 确保文件名有.csv扩展名
        if not filename.endswith('.csv'):
            filename += '.csv'
        
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"结果已导出到: {filename}")
    
    def show_detailed_result(self, index: int):
        """显示详细的搜索结果"""
        if not self.current_results:
            print("没有可显示的搜索结果")
            return
        
        if index < 1 or index > len(self.current_results):
            print(f"无效的索引。请输入 1-{len(self.current_results)} 之间的数字")
            return
        
        result = self.current_results[index - 1]
        
        print(f"\n{'='*60}")
        print(f"详细结果 #{index}")
        print(f"{'='*60}")
        print(f"ID: {result.id}")
        print(f"描述: {result.description}")
        print(f"相似度得分: {result.similarity_score:.6f}")
        print(f"地区: {result.region}")
        print(f"宇宙: {result.universe}")
        print(f"类型: {result.type}")
        print(f"分类: {result.category}")
        print(f"子分类: {result.subcategory}")
        print(f"数据集名称: {result.dataset_name}")
        print(f"覆盖率: {result.coverage:.4f}")
        print(f"用户数量: {result.user_count}")
        print(f"Alpha数量: {result.alpha_count}")
    
    def run(self):
        """运行交互式搜索"""
        # 默认设置
        similarity_method = 'hybrid'
        top_k = 10
        min_score = 0.1
        region_filter = None
        type_filter = None
        
        print("\n欢迎使用数据字段检索器!")
        print("输入 'help' 查看可用命令")
        
        while True:
            try:
                # 获取用户输入
                user_input = input(f"\n[{similarity_method}|top:{top_k}] > ").strip()
                
                if not user_input:
                    continue
                
                # 解析命令
                parts = user_input.split()
                command = parts[0].lower()
                
                if command in ['quit', 'exit']:
                    print("再见!")
                    break
                
                elif command == 'help':
                    self.show_help()
                
                elif command == 'stats':
                    self.show_stats()
                
                elif command == 'search':
                    if len(parts) < 2:
                        print("请提供搜索查询。例如: search market cap")
                        continue
                    
                    query = ' '.join(parts[1:])
                    print(f"搜索: '{query}'...")
                    
                    self.current_results = self.searcher.search(
                        query=query,
                        top_k=top_k,
                        similarity_method=similarity_method,
                        min_score=min_score,
                        region_filter=region_filter,
                        type_filter=type_filter
                    )
                    
                    self.searcher.print_results(self.current_results, show_details=False)
                
                elif command == 'method':
                    if len(parts) < 2:
                        print("请指定相似度方法: cosine, fuzzy, keyword, hybrid")
                        continue
                    
                    new_method = parts[1].lower()
                    if new_method in ['cosine', 'fuzzy', 'keyword', 'hybrid']:
                        similarity_method = new_method
                        print(f"相似度方法已设置为: {similarity_method}")
                    else:
                        print("无效的方法。可用方法: cosine, fuzzy, keyword, hybrid")
                
                elif command == 'top':
                    if len(parts) < 2:
                        print("请指定返回结果数量。例如: top 20")
                        continue
                    
                    try:
                        new_top_k = int(parts[1])
                        if new_top_k > 0:
                            top_k = new_top_k
                            print(f"返回结果数量已设置为: {top_k}")
                        else:
                            print("结果数量必须大于0")
                    except ValueError:
                        print("请输入有效的数字")
                
                elif command == 'filter':
                    if len(parts) < 2:
                        print("请指定过滤器类型: region, type, clear")
                        continue
                    
                    filter_type = parts[1].lower()
                    
                    if filter_type == 'clear':
                        region_filter = None
                        type_filter = None
                        print("所有过滤器已清除")
                    
                    elif filter_type == 'region':
                        if len(parts) < 3:
                            print("请指定地区。例如: filter region CHN")
                            continue
                        region_filter = parts[2]
                        print(f"地区过滤器已设置为: {region_filter}")
                    
                    elif filter_type == 'type':
                        if len(parts) < 3:
                            print("请指定类型。例如: filter type MATRIX")
                            continue
                        type_filter = parts[2]
                        print(f"类型过滤器已设置为: {type_filter}")
                    
                    else:
                        print("无效的过滤器类型。可用类型: region, type, clear")
                
                elif command == 'show':
                    if len(parts) < 2:
                        print("请指定结果索引。例如: show 1")
                        continue
                    
                    try:
                        index = int(parts[1])
                        self.show_detailed_result(index)
                    except ValueError:
                        print("请输入有效的数字")
                
                elif command == 'export':
                    if len(parts) < 2:
                        print("请指定文件名。例如: export results.csv")
                        continue
                    
                    filename = parts[1]
                    self.export_results(filename)
                
                else:
                    print(f"未知命令: {command}。输入 'help' 查看可用命令")
            
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except Exception as e:
                print(f"发生错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据字段检索器 - 交互式搜索界面')
    parser.add_argument('--data-dir', default='split_files', 
                       help='数据文件目录路径 (默认: split_files)')
    parser.add_argument('--query', help='直接执行搜索查询')
    parser.add_argument('--method', default='hybrid', 
                       choices=['cosine', 'fuzzy', 'keyword', 'hybrid'],
                       help='相似度计算方法 (默认: hybrid)')
    parser.add_argument('--top', type=int, default=10,
                       help='返回结果数量 (默认: 10)')
    
    args = parser.parse_args()
    
    try:
        searcher = InteractiveSearcher(args.data_dir)
        
        if args.query:
            # 直接执行搜索
            results = searcher.searcher.search(
                query=args.query,
                top_k=args.top,
                similarity_method=args.method
            )
            searcher.searcher.print_results(results)
        else:
            # 启动交互式模式
            searcher.run()
    
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
