# -*- coding: utf-8 -*-
"""
数据库操作模块
负责创建和管理索引数据库
"""

import sqlite3
import os
from typing import List, Dict, Tuple, Optional
from config import DATABASE_CONFIG


class IndexDatabase:
    """索引数据库管理类"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or DATABASE_CONFIG['index_db']
        self.ensure_db_directory()
        self.init_database()
    
    def ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.Connection(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        return conn
    
    def init_database(self):
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            # 创建字段信息表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS fields (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    field_id TEXT NOT NULL,
                    description TEXT NOT NULL,
                    region TEXT,
                    universe TEXT,
                    field_type TEXT,
                    coverage REAL,
                    dataset_id TEXT,
                    dataset_name TEXT,
                    category_id TEXT,
                    category_name TEXT,
                    subcategory_id TEXT,
                    subcategory_name TEXT,
                    file_source TEXT,
                    UNIQUE(field_id, region, universe)
                )
            ''')
            
            # 创建词汇索引表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS word_index (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    word TEXT NOT NULL,
                    word_type TEXT NOT NULL,  -- 'id' 或 'description'
                    field_id TEXT NOT NULL,
                    position INTEGER,  -- 词在原文中的位置
                    source_text TEXT,  -- 原始文本
                    FOREIGN KEY (field_id) REFERENCES fields (field_id)
                )
            ''')
            
            # 创建类别统计表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS category_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category_name TEXT NOT NULL,
                    field_count INTEGER DEFAULT 0,
                    word_count INTEGER DEFAULT 0,
                    UNIQUE(category_name)
                )
            ''')
            
            # 创建索引以提高查询性能
            conn.execute('CREATE INDEX IF NOT EXISTS idx_word_index_word ON word_index(word)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_word_index_field_id ON word_index(field_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fields_category ON fields(category_name)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fields_region ON fields(region)')
            
            conn.commit()
    
    def insert_field(self, field_data: Dict) -> int:
        """插入字段数据"""
        with self.get_connection() as conn:
            cursor = conn.execute('''
                INSERT OR REPLACE INTO fields 
                (field_id, description, region, universe, field_type, coverage,
                 dataset_id, dataset_name, category_id, category_name, 
                 subcategory_id, subcategory_name, file_source)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                field_data.get('field_id'),
                field_data.get('description'),
                field_data.get('region'),
                field_data.get('universe'),
                field_data.get('field_type'),
                field_data.get('coverage'),
                field_data.get('dataset_id'),
                field_data.get('dataset_name'),
                field_data.get('category_id'),
                field_data.get('category_name'),
                field_data.get('subcategory_id'),
                field_data.get('subcategory_name'),
                field_data.get('file_source')
            ))
            return cursor.lastrowid
    
    def insert_word_index(self, word: str, word_type: str, field_id: str, 
                         position: int = None, source_text: str = None):
        """插入词汇索引"""
        with self.get_connection() as conn:
            conn.execute('''
                INSERT INTO word_index (word, word_type, field_id, position, source_text)
                VALUES (?, ?, ?, ?, ?)
            ''', (word.lower(), word_type, field_id, position, source_text))
    
    def search_by_word(self, word: str, category: str = None, 
                      region: str = None, limit: int = 100) -> List[Dict]:
        """根据词汇搜索字段"""
        query = '''
            SELECT DISTINCT f.*, wi.word, wi.word_type, wi.source_text
            FROM fields f
            JOIN word_index wi ON f.field_id = wi.field_id
            WHERE wi.word LIKE ?
        '''
        params = [f'%{word.lower()}%']
        
        if category:
            query += ' AND f.category_name = ?'
            params.append(category)
        
        if region:
            query += ' AND f.region = ?'
            params.append(region)
        
        query += ' ORDER BY f.category_name, f.field_id LIMIT ?'
        params.append(limit)
        
        with self.get_connection() as conn:
            cursor = conn.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def get_categories(self) -> List[str]:
        """获取所有类别"""
        with self.get_connection() as conn:
            cursor = conn.execute('SELECT DISTINCT category_name FROM fields ORDER BY category_name')
            return [row[0] for row in cursor.fetchall()]
    
    def get_regions(self) -> List[str]:
        """获取所有地区"""
        with self.get_connection() as conn:
            cursor = conn.execute('SELECT DISTINCT region FROM fields ORDER BY region')
            return [row[0] for row in cursor.fetchall()]
    
    def update_category_stats(self):
        """更新类别统计信息"""
        with self.get_connection() as conn:
            # 清空现有统计
            conn.execute('DELETE FROM category_stats')
            
            # 重新计算统计
            conn.execute('''
                INSERT INTO category_stats (category_name, field_count, word_count)
                SELECT 
                    f.category_name,
                    COUNT(DISTINCT f.field_id) as field_count,
                    COUNT(wi.word) as word_count
                FROM fields f
                LEFT JOIN word_index wi ON f.field_id = wi.field_id
                GROUP BY f.category_name
            ''')
            conn.commit()
    
    def get_stats(self) -> Dict:
        """获取数据库统计信息"""
        with self.get_connection() as conn:
            stats = {}
            
            # 总字段数
            cursor = conn.execute('SELECT COUNT(*) FROM fields')
            stats['total_fields'] = cursor.fetchone()[0]
            
            # 总词汇数
            cursor = conn.execute('SELECT COUNT(DISTINCT word) FROM word_index')
            stats['total_words'] = cursor.fetchone()[0]
            
            # 按类别统计
            cursor = conn.execute('''
                SELECT category_name, COUNT(*) 
                FROM fields 
                GROUP BY category_name 
                ORDER BY COUNT(*) DESC
            ''')
            stats['by_category'] = dict(cursor.fetchall())
            
            # 按地区统计
            cursor = conn.execute('''
                SELECT region, COUNT(*) 
                FROM fields 
                GROUP BY region 
                ORDER BY COUNT(*) DESC
            ''')
            stats['by_region'] = dict(cursor.fetchall())
            
            return stats
