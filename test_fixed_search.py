# -*- coding: utf-8 -*-
"""
测试修复后的搜索功能
"""

from database import IndexDatabase
from tokenizer import EconomicsTokenizer
from app import SearchEngine


def test_volume_search():
    """测试volume搜索功能"""
    print("=== 测试修复后的volume搜索 ===")
    
    search_engine = SearchEngine()
    
    # 搜索volume
    results = search_engine.search("volume")
    
    print(f"搜索'volume'找到 {len(results)} 个结果：")
    
    # 分类显示结果
    id_matches = [r for r in results if r.get('id_matches', 0) > 0]
    desc_matches = [r for r in results if r.get('desc_matches', 0) > 0 and r.get('id_matches', 0) == 0]
    
    print(f"\nID匹配结果 ({len(id_matches)} 个)：")
    for i, result in enumerate(id_matches[:10], 1):
        print(f"{i}. 字段ID: {result['field_id']}")
        print(f"   描述: {result['description']}")
        print(f"   ID匹配: {result['id_matches']}, 描述匹配: {result['desc_matches']}")
        print(f"   匹配词汇: {result['matched_words']}")
        print()
    
    print(f"\n描述匹配结果 ({len(desc_matches)} 个)：")
    for i, result in enumerate(desc_matches[:5], 1):
        print(f"{i}. 字段ID: {result['field_id']}")
        print(f"   描述: {result['description']}")
        print(f"   ID匹配: {result['id_matches']}, 描述匹配: {result['desc_matches']}")
        print()


def test_file_source_search():
    """测试按文件来源搜索"""
    print("\n=== 测试按数据表搜索 ===")
    
    search_engine = SearchEngine()
    
    # 获取所有文件来源
    file_sources = search_engine.get_file_sources()
    print(f"可用数据表: {file_sources}")
    
    # 在特定文件中搜索
    if file_sources:
        test_file = file_sources[0]
        print(f"\n在数据表 '{test_file}' 中搜索 'volume'：")
        
        results = search_engine.search("volume", file_source=test_file)
        print(f"找到 {len(results)} 个结果")
        
        for i, result in enumerate(results[:5], 1):
            print(f"{i}. {result['field_id']} - {result['description'][:50]}...")


def test_specific_volume_ids():
    """测试特定包含volume的ID搜索"""
    print("\n=== 测试特定volume ID搜索 ===")
    
    db = IndexDatabase()
    search_engine = SearchEngine()
    
    # 查找包含volume的字段ID
    with db.get_connection() as conn:
        cursor = conn.execute('''
            SELECT DISTINCT field_id, description, file_source
            FROM fields 
            WHERE field_id LIKE '%vol%' OR field_id LIKE '%volume%'
            ORDER BY field_id
            LIMIT 10
        ''')
        
        volume_ids = cursor.fetchall()
        
        print("数据库中包含volume的字段ID：")
        for field_id, description, file_source in volume_ids:
            print(f"  {field_id} - {description[:50]}... ({file_source})")
        
        if volume_ids:
            # 测试搜索这些ID
            print(f"\n搜索 'vol' 应该能找到这些字段：")
            results = search_engine.search("vol")
            
            found_ids = [r['field_id'] for r in results if r.get('id_matches', 0) > 0]
            expected_ids = [row[0] for row in volume_ids]
            
            print(f"期望找到的ID: {expected_ids}")
            print(f"实际找到的ID: {found_ids[:10]}")
            
            # 检查匹配情况
            matched = set(found_ids) & set(expected_ids)
            print(f"成功匹配的ID: {list(matched)}")


def test_search_without_limits():
    """测试无限制搜索"""
    print("\n=== 测试无限制搜索 ===")
    
    search_engine = SearchEngine()
    
    # 搜索一个常见词汇
    results = search_engine.search("price")
    
    print(f"搜索'price'找到 {len(results)} 个结果（无限制）")
    
    # 统计不同类型的匹配
    id_matches = sum(1 for r in results if r.get('id_matches', 0) > 0)
    desc_matches = sum(1 for r in results if r.get('desc_matches', 0) > 0)
    
    print(f"ID匹配: {id_matches} 个")
    print(f"描述匹配: {desc_matches} 个")
    
    # 显示前几个结果
    print("\n前10个结果：")
    for i, result in enumerate(results[:10], 1):
        match_type = "ID匹配" if result.get('id_matches', 0) > 0 else "描述匹配"
        print(f"{i}. [{match_type}] {result['field_id']} - {result['description'][:40]}...")


def test_database_stats():
    """测试数据库统计"""
    print("\n=== 数据库统计信息 ===")
    
    search_engine = SearchEngine()
    
    stats = search_engine.get_stats()
    categories = search_engine.get_categories()
    regions = search_engine.get_regions()
    datasets = search_engine.get_datasets()
    file_sources = search_engine.get_file_sources()
    
    print(f"总字段数: {stats['total_fields']}")
    print(f"总词汇数: {stats['total_words']}")
    print(f"类别数: {len(categories)}")
    print(f"地区数: {len(regions)}")
    print(f"数据集数: {len(datasets)}")
    print(f"数据表数: {len(file_sources)}")
    
    print(f"\n数据表列表: {file_sources}")


if __name__ == "__main__":
    print("开始测试修复后的搜索功能...")
    
    # 1. 测试volume搜索
    test_volume_search()
    
    # 2. 测试按文件来源搜索
    test_file_source_search()
    
    # 3. 测试特定volume ID
    test_specific_volume_ids()
    
    # 4. 测试无限制搜索
    test_search_without_limits()
    
    # 5. 测试数据库统计
    test_database_stats()
    
    print("\n测试完成！")
