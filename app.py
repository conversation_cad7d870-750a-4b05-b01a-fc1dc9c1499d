# -*- coding: utf-8 -*-
"""
经济学数据字段搜索引擎 Web 应用
"""

from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
from database import IndexDatabase
from tokenizer import EconomicsTokenizer
import json


app = Flask(__name__)
CORS(app)


class SearchEngine:
    """搜索引擎类"""
    
    def __init__(self):
        self.db = IndexDatabase()
        self.tokenizer = EconomicsTokenizer()
    
    def search(self, query: str, category: str = None, region: str = None,
              dataset: str = None, file_source: str = None):
        """搜索字段"""
        if not query.strip():
            return []

        # 对查询进行分词
        query_tokens = self.tokenizer.tokenize_description(query)

        if not query_tokens:
            return []

        # 搜索每个词汇
        all_results = []
        for token in query_tokens:
            results = self.db.search_by_word(token, category, region, dataset, file_source)
            all_results.extend(results)

        # 去重并按相关性排序
        unique_results = {}
        for result in all_results:
            key = (result['field_id'], result['region'], result['file_source'])
            if key not in unique_results:
                unique_results[key] = result
                unique_results[key]['match_count'] = 1
                unique_results[key]['matched_words'] = [result['word']]
                unique_results[key]['id_matches'] = 1 if result['word_type'] == 'id' else 0
                unique_results[key]['desc_matches'] = 1 if result['word_type'] == 'description' else 0
            else:
                unique_results[key]['match_count'] += 1
                if result['word'] not in unique_results[key]['matched_words']:
                    unique_results[key]['matched_words'].append(result['word'])
                if result['word_type'] == 'id':
                    unique_results[key]['id_matches'] += 1
                else:
                    unique_results[key]['desc_matches'] += 1

        # 按相关性排序：完全匹配优先，然后ID匹配，最后按匹配次数
        def get_sort_key(result):
            # 检查是否有完全匹配（字段ID等于查询词）
            exact_match = 1 if any(token.lower() == result['field_id'].lower() for token in query_tokens) else 0
            return (exact_match, result['id_matches'], result['match_count'], len(result['matched_words']))

        sorted_results = sorted(unique_results.values(), key=get_sort_key, reverse=True)

        return sorted_results
    
    def get_categories(self):
        """获取所有类别"""
        return self.db.get_categories()
    
    def get_regions(self):
        """获取所有地区"""
        return self.db.get_regions()

    def get_datasets(self):
        """获取所有数据集"""
        return self.db.get_datasets()

    def get_file_sources(self):
        """获取所有文件来源"""
        return self.db.get_file_sources()

    def get_stats(self):
        """获取统计信息"""
        return self.db.get_stats()


# 创建搜索引擎实例
search_engine = SearchEngine()


@app.route('/')
def index():
    """主页"""
    stats = search_engine.get_stats()
    categories = search_engine.get_categories()
    regions = search_engine.get_regions()
    datasets = search_engine.get_datasets()
    file_sources = search_engine.get_file_sources()

    return render_template('index.html',
                         stats=stats,
                         categories=categories,
                         regions=regions,
                         datasets=datasets,
                         file_sources=file_sources)


@app.route('/api/search')
def api_search():
    """搜索API"""
    query = request.args.get('q', '').strip()
    category = request.args.get('category', '').strip()
    region = request.args.get('region', '').strip()
    dataset = request.args.get('dataset', '').strip()
    file_source = request.args.get('file_source', '').strip()

    if not query:
        return jsonify({'error': '查询不能为空', 'results': []})

    # 执行搜索
    results = search_engine.search(
        query=query,
        category=category if category else None,
        region=region if region else None,
        dataset=dataset if dataset else None,
        file_source=file_source if file_source else None
    )

    # 格式化结果
    formatted_results = []
    for result in results:
        formatted_results.append({
            'field_id': result['field_id'],
            'description': result['description'],
            'region': result['region'],
            'category_name': result['category_name'],
            'dataset_name': result['dataset_name'],
            'file_source': result['file_source'],
            'match_count': result['match_count'],
            'matched_words': result.get('matched_words', []),
            'id_matches': result.get('id_matches', 0),
            'desc_matches': result.get('desc_matches', 0)
        })

    return jsonify({
        'query': query,
        'total_results': len(formatted_results),
        'results': formatted_results
    })


@app.route('/api/categories')
def api_categories():
    """获取类别API"""
    return jsonify(search_engine.get_categories())


@app.route('/api/regions')
def api_regions():
    """获取地区API"""
    return jsonify(search_engine.get_regions())


@app.route('/api/datasets')
def api_datasets():
    """获取数据集API"""
    return jsonify(search_engine.get_datasets())


@app.route('/api/file_sources')
def api_file_sources():
    """获取文件来源API"""
    return jsonify(search_engine.get_file_sources())


@app.route('/api/stats')
def api_stats():
    """获取统计信息API"""
    return jsonify(search_engine.get_stats())


if __name__ == '__main__':
    # 检查数据库是否存在数据
    stats = search_engine.get_stats()
    if stats['total_fields'] == 0:
        print("警告: 数据库为空，请先运行 build_index.py 构建索引")
    else:
        print(f"数据库包含 {stats['total_fields']} 个字段，{stats['total_words']} 个词汇")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
