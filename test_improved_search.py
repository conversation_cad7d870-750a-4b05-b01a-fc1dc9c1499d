# -*- coding: utf-8 -*-
"""
测试改进后的搜索逻辑
"""

from app import SearchEngine
import requests


def test_quarterly_earnings_improved():
    """测试改进后的quarterly earnings搜索"""
    print("=== 测试改进后的quarterly earnings搜索 ===")
    
    search_engine = SearchEngine()
    
    # 搜索quarterly earnings
    results = search_engine.search("quarterly earnings")
    
    print(f"搜索'quarterly earnings'找到 {len(results)} 个结果")
    
    # 显示前20个结果，重点关注同时匹配两个词的
    print("\n前20个结果:")
    for i, result in enumerate(results[:20], 1):
        id_matches = result.get('id_matches', 0)
        desc_matches = result.get('desc_matches', 0)
        match_count = result.get('match_count', 0)
        matched_words = result.get('matched_words', [])
        
        # 检查是否同时匹配quarterly和earnings
        has_quarterly = any('quarterly' in word.lower() for word in matched_words)
        has_earnings = any('earning' in word.lower() for word in matched_words)
        both_match = "🎯双词匹配" if has_quarterly and has_earnings else ""
        
        match_type = "🔑ID" if id_matches > 0 else "📝DESC"
        
        print(f"{i:2d}. {match_type} {both_match} - {result['field_id']}")
        print(f"     描述: {result['description'][:60]}...")
        print(f"     匹配词汇: {matched_words}")
        print(f"     匹配统计: ID={id_matches}, 描述={desc_matches}, 总计={match_count}")
        print()


def test_other_multi_word_queries():
    """测试其他多词查询"""
    print("\n=== 测试其他多词查询 ===")
    
    search_engine = SearchEngine()
    
    test_queries = [
        "net income",
        "market cap", 
        "price earnings",
        "cash flow",
        "return equity"
    ]
    
    for query in test_queries:
        print(f"\n搜索'{query}':")
        results = search_engine.search(query)
        
        query_words = query.split()
        
        # 统计同时匹配多个词的结果
        multi_match_count = 0
        for result in results[:10]:  # 只检查前10个
            matched_words = result.get('matched_words', [])
            matched_query_words = 0
            
            for q_word in query_words:
                if any(q_word.lower() in word.lower() for word in matched_words):
                    matched_query_words += 1
            
            if matched_query_words >= 2:
                multi_match_count += 1
        
        print(f"   总结果: {len(results)}")
        print(f"   前10个中同时匹配多词的: {multi_match_count}")
        
        # 显示前3个结果
        for i, result in enumerate(results[:3], 1):
            matched_words = result.get('matched_words', [])
            print(f"   {i}. {result['field_id']} - 匹配词汇: {matched_words}")


def test_web_api_improved():
    """测试Web API的改进效果"""
    print("\n=== 测试Web API的改进效果 ===")
    
    try:
        response = requests.get("http://127.0.0.1:5000/api/search?q=quarterly%20earnings", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"API搜索'quarterly earnings':")
            print(f"   返回 {data['total_results']} 个结果")
            
            # 检查前10个结果中同时匹配两个词的数量
            multi_match_count = 0
            for result in data['results'][:10]:
                matched_words = result.get('matched_words', [])
                has_quarterly = any('quarterly' in word.lower() for word in matched_words)
                has_earnings = any('earning' in word.lower() for word in matched_words)
                
                if has_quarterly and has_earnings:
                    multi_match_count += 1
            
            print(f"   前10个结果中同时匹配两词的: {multi_match_count}")
            
            print("\n   前10个结果:")
            for i, result in enumerate(data['results'][:10], 1):
                matched_words = result.get('matched_words', [])
                has_quarterly = any('quarterly' in word.lower() for word in matched_words)
                has_earnings = any('earning' in word.lower() for word in matched_words)
                both_match = "🎯" if has_quarterly and has_earnings else ""
                
                print(f"   {i:2d}. {both_match}{result['field_id']} - {result['description'][:40]}...")
        
        else:
            print(f"API请求失败: {response.status_code}")
    
    except Exception as e:
        print(f"无法测试API: {e}")


def find_best_quarterly_earnings_matches():
    """找到最佳的quarterly earnings匹配"""
    print("\n=== 寻找最佳的quarterly earnings匹配 ===")
    
    search_engine = SearchEngine()
    
    # 搜索quarterly earnings
    results = search_engine.search("quarterly earnings")
    
    # 找到同时包含两个词的结果
    best_matches = []
    for result in results:
        matched_words = result.get('matched_words', [])
        has_quarterly = any('quarterly' in word.lower() for word in matched_words)
        has_earnings = any('earning' in word.lower() for word in matched_words)
        
        if has_quarterly and has_earnings:
            best_matches.append(result)
    
    print(f"找到 {len(best_matches)} 个同时匹配'quarterly'和'earnings'的字段:")
    
    for i, result in enumerate(best_matches, 1):
        print(f"{i}. {result['field_id']}")
        print(f"   描述: {result['description']}")
        print(f"   类别: {result['category_name']}")
        print(f"   文件: {result['file_source']}")
        print(f"   匹配词汇: {result.get('matched_words', [])}")
        print()


def suggest_better_search_terms():
    """建议更好的搜索词"""
    print("\n=== 建议更好的搜索词 ===")
    
    search_engine = SearchEngine()
    
    # 基于数据库中实际存在的字段，建议更好的搜索词
    suggestions = [
        ("eps", "每股收益 - Earnings Per Share"),
        ("ebitda", "息税折旧摊销前利润 - Earnings Before Interest, Taxes, Depreciation, and Amortization"),
        ("net income", "净收入"),
        ("quarterly", "季度相关数据"),
        ("fiscal quarter", "财政季度"),
        ("quarter", "季度数据"),
        ("profit", "利润相关"),
        ("revenue", "收入相关")
    ]
    
    print("如果您在寻找季度收益相关数据，建议尝试这些搜索词:")
    
    for term, description in suggestions:
        results = search_engine.search(term)
        print(f"\n'{term}' ({description}): {len(results)} 个结果")
        
        if results:
            # 显示最相关的结果
            best = results[0]
            print(f"   最佳匹配: {best['field_id']}")
            print(f"   描述: {best['description'][:60]}...")


if __name__ == "__main__":
    print("开始测试改进后的搜索逻辑...")
    
    # 1. 测试改进后的quarterly earnings搜索
    test_quarterly_earnings_improved()
    
    # 2. 测试其他多词查询
    test_other_multi_word_queries()
    
    # 3. 测试Web API
    test_web_api_improved()
    
    # 4. 找到最佳匹配
    find_best_quarterly_earnings_matches()
    
    # 5. 建议更好的搜索词
    suggest_better_search_terms()
    
    print("\n🎉 测试完成！")
