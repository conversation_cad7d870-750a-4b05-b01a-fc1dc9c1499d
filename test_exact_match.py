# -*- coding: utf-8 -*-
"""
测试完全匹配优先级
"""

from app import SearchEngine
import requests


def test_exact_match_priority():
    """测试完全匹配优先级"""
    print("=== 测试完全匹配优先级 ===")
    
    search_engine = SearchEngine()
    
    # 测试搜索volume
    results = search_engine.search("volume")
    
    print(f"搜索'volume'找到 {len(results)} 个结果")
    
    # 显示前20个结果
    print("\n前20个结果:")
    for i, result in enumerate(results[:20], 1):
        id_matches = result.get('id_matches', 0)
        desc_matches = result.get('desc_matches', 0)
        match_count = result.get('match_count', 0)
        
        # 检查是否完全匹配
        exact_match = "🎯完全匹配" if result['field_id'].lower() == 'volume' else ""
        
        if id_matches > 0:
            match_type = "🔑ID匹配"
        else:
            match_type = "📝描述匹配"
        
        print(f"{i:2d}. {match_type} {exact_match} - {result['field_id']} - 匹配:{match_count}")
        if i <= 5:  # 显示前5个的详细信息
            print(f"     描述: {result['description'][:50]}...")
            print(f"     ID匹配:{id_matches}, 描述匹配:{desc_matches}, 匹配词汇:{result.get('matched_words', [])}")
    
    # 检查ID为volume的字段位置
    volume_positions = []
    for i, result in enumerate(results):
        if result['field_id'] == 'volume':
            volume_positions.append(i + 1)
    
    print(f"\nID为'volume'的字段现在的位置: {volume_positions}")


def test_other_exact_matches():
    """测试其他完全匹配的情况"""
    print("\n=== 测试其他完全匹配 ===")
    
    search_engine = SearchEngine()
    
    # 测试一些可能有完全匹配的查询
    test_queries = ['price', 'beta', 'close', 'open']
    
    for query in test_queries:
        print(f"\n搜索'{query}':")
        results = search_engine.search(query)
        
        # 查找完全匹配
        exact_matches = [r for r in results if r['field_id'].lower() == query.lower()]
        
        if exact_matches:
            print(f"  找到 {len(exact_matches)} 个完全匹配:")
            for i, result in enumerate(exact_matches, 1):
                position = next((j for j, r in enumerate(results) if 
                               r['field_id'] == result['field_id'] and 
                               r['region'] == result['region']), -1) + 1
                print(f"    {i}. {result['field_id']} - 位置: 第{position}位")
        else:
            print(f"  没有找到完全匹配")
        
        # 显示前3个结果
        print(f"  前3个结果:")
        for i, result in enumerate(results[:3], 1):
            exact = "🎯" if result['field_id'].lower() == query.lower() else ""
            print(f"    {i}. {exact}{result['field_id']} - 匹配:{result.get('match_count', 0)}")


def test_web_api_exact_match():
    """测试Web API的完全匹配"""
    print("\n=== 测试Web API的完全匹配 ===")
    
    try:
        response = requests.get("http://127.0.0.1:5000/api/search?q=volume", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"API返回 {data['total_results']} 个结果")
            
            # 查找ID为volume的结果位置
            volume_positions = []
            for i, result in enumerate(data['results']):
                if result['field_id'] == 'volume':
                    volume_positions.append(i + 1)
            
            print(f"ID为'volume'的字段在API中的位置: {volume_positions}")
            
            # 显示前10个结果
            print("\nAPI前10个结果:")
            for i, result in enumerate(data['results'][:10], 1):
                exact = "🎯" if result['field_id'] == 'volume' else ""
                match_type = "🔑ID" if result.get('id_matches', 0) > 0 else "📝DESC"
                print(f"{i:2d}. {match_type} {exact} - {result['field_id']} - 匹配:{result.get('match_count', 0)}")
        
        else:
            print(f"API请求失败: {response.status_code}")
    
    except Exception as e:
        print(f"无法测试API: {e}")


def test_case_insensitive():
    """测试大小写不敏感的完全匹配"""
    print("\n=== 测试大小写不敏感的完全匹配 ===")
    
    search_engine = SearchEngine()
    
    # 测试不同大小写
    test_cases = ['volume', 'Volume', 'VOLUME']
    
    for query in test_cases:
        print(f"\n搜索'{query}':")
        results = search_engine.search(query)
        
        # 查找前5个结果
        for i, result in enumerate(results[:5], 1):
            exact = "🎯" if result['field_id'].lower() == query.lower() else ""
            print(f"  {i}. {exact}{result['field_id']}")


if __name__ == "__main__":
    print("开始测试完全匹配优先级...")
    
    # 1. 测试volume的完全匹配
    test_exact_match_priority()
    
    # 2. 测试其他完全匹配
    test_other_exact_matches()
    
    # 3. 测试Web API
    test_web_api_exact_match()
    
    # 4. 测试大小写不敏感
    test_case_insensitive()
    
    print("\n🎉 完全匹配测试完成！")
