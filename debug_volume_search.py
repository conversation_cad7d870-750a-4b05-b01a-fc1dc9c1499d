# -*- coding: utf-8 -*-
"""
深度调试volume搜索问题
"""

from database import IndexDatabase
from tokenizer import EconomicsTokenizer
from app import SearchEngine


def check_volume_field_exists():
    """检查是否存在ID为volume的字段"""
    print("=== 检查是否存在ID为'volume'的字段 ===")
    
    db = IndexDatabase()
    
    with db.get_connection() as conn:
        # 查找ID完全等于volume的字段
        cursor = conn.execute('''
            SELECT field_id, description, region, file_source
            FROM fields 
            WHERE field_id = 'volume'
        ''')
        
        exact_matches = cursor.fetchall()
        print(f"ID完全等于'volume'的字段: {len(exact_matches)} 个")
        
        for field_id, description, region, file_source in exact_matches:
            print(f"  字段ID: {field_id}")
            print(f"  描述: {description}")
            print(f"  地区: {region}")
            print(f"  文件: {file_source}")
            print()
        
        # 查找ID包含volume的字段
        cursor = conn.execute('''
            SELECT field_id, description, region, file_source
            FROM fields 
            WHERE field_id LIKE '%volume%'
            ORDER BY field_id
        ''')
        
        contains_matches = cursor.fetchall()
        print(f"ID包含'volume'的字段: {len(contains_matches)} 个")
        
        for field_id, description, region, file_source in contains_matches:
            print(f"  字段ID: {field_id}")
            print(f"  描述: {description[:50]}...")
            print(f"  地区: {region}, 文件: {file_source}")
            print()


def check_volume_word_index():
    """检查volume在词汇索引中的情况"""
    print("=== 检查'volume'在词汇索引中的情况 ===")
    
    db = IndexDatabase()
    
    with db.get_connection() as conn:
        # 查找词汇完全等于volume的索引
        cursor = conn.execute('''
            SELECT word, word_type, field_id, source_text
            FROM word_index 
            WHERE word = 'volume'
            ORDER BY word_type, field_id
        ''')
        
        volume_indexes = cursor.fetchall()
        print(f"词汇完全等于'volume'的索引: {len(volume_indexes)} 个")
        
        id_indexes = [r for r in volume_indexes if r[1] == 'id']
        desc_indexes = [r for r in volume_indexes if r[1] == 'description']
        
        print(f"  ID类型索引: {len(id_indexes)} 个")
        print(f"  描述类型索引: {len(desc_indexes)} 个")
        
        print("\nID类型的volume索引:")
        for word, word_type, field_id, source_text in id_indexes:
            print(f"  字段ID: {field_id}, 原文: {source_text}")
        
        print("\n前10个描述类型的volume索引:")
        for word, word_type, field_id, source_text in desc_indexes[:10]:
            print(f"  字段ID: {field_id}, 原文: {source_text[:50]}...")


def test_tokenizer_on_volume():
    """测试分词器对'volume'的处理"""
    print("\n=== 测试分词器对'volume'的处理 ===")
    
    tokenizer = EconomicsTokenizer()
    
    # 测试ID分词
    test_ids = ['volume', 'Volume', 'VOLUME']
    
    print("ID分词测试:")
    for test_id in test_ids:
        tokens = tokenizer.tokenize_field_id(test_id)
        print(f"  '{test_id}' -> {tokens}")
    
    # 测试描述分词
    test_descriptions = ['volume', 'Volume', 'trading volume', 'daily volume']
    
    print("\n描述分词测试:")
    for desc in test_descriptions:
        tokens = tokenizer.tokenize_description(desc)
        print(f"  '{desc}' -> {tokens}")


def test_database_search_directly():
    """直接测试数据库搜索"""
    print("\n=== 直接测试数据库搜索 ===")
    
    db = IndexDatabase()
    
    # 测试不同的搜索词
    test_words = ['volume', 'Volume', 'vol']
    
    for word in test_words:
        print(f"\n搜索词汇: '{word}'")
        results = db.search_by_word(word, limit=10)
        
        print(f"  找到 {len(results)} 个结果")
        
        # 分类显示
        id_results = [r for r in results if r['word_type'] == 'id']
        desc_results = [r for r in results if r['word_type'] == 'description']
        
        print(f"  ID匹配: {len(id_results)} 个")
        print(f"  描述匹配: {len(desc_results)} 个")
        
        if id_results:
            print("  ID匹配结果:")
            for r in id_results[:5]:
                print(f"    {r['field_id']} (匹配词: {r['word']}, 原文: {r['source_text']})")


def test_search_engine_logic():
    """测试搜索引擎逻辑"""
    print("\n=== 测试搜索引擎逻辑 ===")
    
    search_engine = SearchEngine()
    
    # 测试搜索volume
    print("搜索引擎搜索'volume':")
    results = search_engine.search("volume")
    
    print(f"总结果: {len(results)}")
    
    # 检查前10个结果
    print("\n前10个结果:")
    for i, result in enumerate(results[:10], 1):
        print(f"{i}. 字段ID: {result['field_id']}")
        print(f"   描述: {result['description'][:50]}...")
        print(f"   ID匹配: {result.get('id_matches', 0)}")
        print(f"   描述匹配: {result.get('desc_matches', 0)}")
        print(f"   匹配词汇: {result.get('matched_words', [])}")
        print()
    
    # 专门查找ID为volume的结果
    volume_id_results = [r for r in results if r['field_id'] == 'volume']
    print(f"ID为'volume'的结果: {len(volume_id_results)} 个")
    
    for result in volume_id_results:
        print(f"  字段ID: {result['field_id']}")
        print(f"  描述: {result['description']}")
        print(f"  匹配情况: ID匹配={result.get('id_matches', 0)}, 描述匹配={result.get('desc_matches', 0)}")


def analyze_search_logic():
    """分析当前搜索逻辑"""
    print("\n=== 分析当前搜索逻辑 ===")
    
    print("当前搜索逻辑步骤:")
    print("1. 用户输入查询词 -> 对查询进行描述分词")
    print("2. 对每个分词结果调用 db.search_by_word()")
    print("3. 合并所有结果并去重")
    print("4. 按ID匹配优先排序")
    
    # 模拟搜索过程
    tokenizer = EconomicsTokenizer()
    query = "volume"
    
    print(f"\n模拟搜索'{query}'的过程:")
    
    # 步骤1: 分词
    query_tokens = tokenizer.tokenize_description(query)
    print(f"1. 查询分词结果: {query_tokens}")
    
    # 步骤2: 搜索每个词汇
    db = IndexDatabase()
    all_results = []
    
    for token in query_tokens:
        print(f"2. 搜索词汇'{token}':")
        results = db.search_by_word(token, limit=5)
        print(f"   找到 {len(results)} 个结果")
        
        for r in results:
            print(f"   - {r['field_id']} ({r['word_type']}: {r['word']})")
        
        all_results.extend(results)
    
    print(f"3. 总共收集到 {len(all_results)} 个结果")


def check_exact_volume_search():
    """检查精确的volume搜索"""
    print("\n=== 检查精确的volume搜索 ===")
    
    db = IndexDatabase()
    
    with db.get_connection() as conn:
        # 直接SQL查询
        cursor = conn.execute('''
            SELECT DISTINCT f.field_id, f.description, wi.word, wi.word_type, wi.source_text
            FROM fields f
            JOIN word_index wi ON f.field_id = wi.field_id
            WHERE wi.word = 'volume'
            ORDER BY wi.word_type, f.field_id
        ''')
        
        results = cursor.fetchall()
        print(f"SQL直接查询'volume'找到 {len(results)} 个结果:")
        
        for field_id, description, word, word_type, source_text in results:
            print(f"  字段ID: {field_id}")
            print(f"  描述: {description[:50]}...")
            print(f"  匹配类型: {word_type}")
            print(f"  原文: {source_text}")
            print()


if __name__ == "__main__":
    print("开始深度调试volume搜索问题...")
    
    # 1. 检查是否存在ID为volume的字段
    check_volume_field_exists()
    
    # 2. 检查volume在词汇索引中的情况
    check_volume_word_index()
    
    # 3. 测试分词器
    test_tokenizer_on_volume()
    
    # 4. 直接测试数据库搜索
    test_database_search_directly()
    
    # 5. 测试搜索引擎逻辑
    test_search_engine_logic()
    
    # 6. 分析搜索逻辑
    analyze_search_logic()
    
    # 7. 检查精确搜索
    check_exact_volume_search()
    
    print("\n调试完成！")
