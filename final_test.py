# -*- coding: utf-8 -*-
"""
最终测试脚本 - 验证所有改进功能
"""

import requests
import json


def test_api_search():
    """测试API搜索功能"""
    print("=== 测试API搜索功能 ===")
    
    base_url = "http://127.0.0.1:5000"
    
    # 1. 测试基本搜索
    print("1. 测试基本搜索 'volume':")
    response = requests.get(f"{base_url}/api/search?q=volume")
    data = response.json()
    
    print(f"   找到 {data['total_results']} 个结果")
    
    # 统计ID匹配和描述匹配
    id_matches = sum(1 for r in data['results'] if r.get('id_matches', 0) > 0)
    desc_matches = sum(1 for r in data['results'] if r.get('desc_matches', 0) > 0)
    
    print(f"   ID匹配: {id_matches} 个")
    print(f"   描述匹配: {desc_matches} 个")
    
    # 显示前几个ID匹配的结果
    id_match_results = [r for r in data['results'] if r.get('id_matches', 0) > 0][:3]
    print("   前3个ID匹配结果:")
    for i, result in enumerate(id_match_results, 1):
        print(f"     {i}. {result['field_id']} - {result['description'][:50]}...")
    
    # 2. 测试按数据表搜索
    print("\n2. 测试按数据表搜索:")
    
    # 获取数据表列表
    response = requests.get(f"{base_url}/api/file_sources")
    file_sources = response.json()
    print(f"   可用数据表: {file_sources}")
    
    if file_sources:
        test_file = file_sources[0]
        print(f"   在数据表 '{test_file}' 中搜索 'price':")
        
        response = requests.get(f"{base_url}/api/search?q=price&file_source={test_file}")
        data = response.json()
        
        print(f"   找到 {data['total_results']} 个结果")
        
        # 显示前几个结果
        for i, result in enumerate(data['results'][:3], 1):
            print(f"     {i}. {result['field_id']} - {result['description'][:40]}...")
    
    # 3. 测试按类别搜索
    print("\n3. 测试按类别搜索:")
    
    response = requests.get(f"{base_url}/api/categories")
    categories = response.json()
    print(f"   可用类别: {categories}")
    
    if "Price Volume" in categories:
        print("   在 'Price Volume' 类别中搜索 'volume':")
        
        response = requests.get(f"{base_url}/api/search?q=volume&category=Price Volume")
        data = response.json()
        
        print(f"   找到 {data['total_results']} 个结果")
        
        for i, result in enumerate(data['results'][:3], 1):
            print(f"     {i}. {result['field_id']} - {result['description'][:40]}...")
    
    # 4. 测试组合搜索
    print("\n4. 测试组合搜索（类别+地区+数据表）:")
    
    if file_sources and "Price Volume" in categories:
        response = requests.get(f"{base_url}/api/search?q=price&category=Price Volume&region=USA&file_source={file_sources[1] if len(file_sources) > 1 else file_sources[0]}")
        data = response.json()
        
        print(f"   组合条件搜索找到 {data['total_results']} 个结果")


def test_specific_cases():
    """测试特定案例"""
    print("\n=== 测试特定案例 ===")
    
    base_url = "http://127.0.0.1:5000"
    
    # 测试包含volume的特定ID
    test_cases = [
        ("volume", "应该找到ID中包含volume的字段"),
        ("vol", "应该找到ID中包含vol的字段"),
        ("price", "应该找到ID中包含price的字段"),
        ("beta", "应该找到ID中包含beta的字段")
    ]
    
    for query, description in test_cases:
        print(f"\n测试查询: '{query}' - {description}")
        
        response = requests.get(f"{base_url}/api/search?q={query}")
        data = response.json()
        
        # 统计结果
        total = data['total_results']
        id_matches = sum(1 for r in data['results'] if r.get('id_matches', 0) > 0)
        
        print(f"  总结果: {total}, ID匹配: {id_matches}")
        
        # 显示一些ID匹配的例子
        id_examples = [r['field_id'] for r in data['results'] if r.get('id_matches', 0) > 0][:5]
        if id_examples:
            print(f"  ID匹配示例: {id_examples}")


def test_no_limits():
    """测试无限制搜索"""
    print("\n=== 测试无限制搜索 ===")
    
    base_url = "http://127.0.0.1:5000"
    
    # 搜索一个常见词汇，应该返回大量结果
    response = requests.get(f"{base_url}/api/search?q=market")
    data = response.json()
    
    print(f"搜索 'market' 找到 {data['total_results']} 个结果（无限制）")
    
    # 验证确实没有限制
    if data['total_results'] > 100:
        print("✅ 确认已移除搜索结果数量限制")
    else:
        print("❌ 搜索结果可能仍有限制")
    
    # 统计不同匹配类型
    id_matches = sum(1 for r in data['results'] if r.get('id_matches', 0) > 0)
    desc_matches = sum(1 for r in data['results'] if r.get('desc_matches', 0) > 0)
    
    print(f"ID匹配: {id_matches}, 描述匹配: {desc_matches}")


def test_data_table_functionality():
    """测试数据表功能"""
    print("\n=== 测试数据表功能 ===")
    
    base_url = "http://127.0.0.1:5000"
    
    # 获取统计信息
    response = requests.get(f"{base_url}/api/stats")
    stats = response.json()
    
    response = requests.get(f"{base_url}/api/file_sources")
    file_sources = response.json()
    
    print(f"数据库统计:")
    print(f"  总字段数: {stats['total_fields']}")
    print(f"  总词汇数: {stats['total_words']}")
    print(f"  数据表数: {len(file_sources)}")
    
    print(f"\n数据表列表:")
    for i, file_source in enumerate(file_sources, 1):
        print(f"  {i}. {file_source}")
    
    # 测试每个数据表的搜索
    print(f"\n测试各数据表中的 'volume' 搜索:")
    for file_source in file_sources:
        response = requests.get(f"{base_url}/api/search?q=volume&file_source={file_source}")
        data = response.json()
        
        print(f"  {file_source}: {data['total_results']} 个结果")


def main():
    """主测试函数"""
    print("开始最终功能测试...")
    print("确保Web应用正在运行在 http://127.0.0.1:5000")
    
    try:
        # 测试服务器是否可用
        response = requests.get("http://127.0.0.1:5000/api/stats", timeout=5)
        if response.status_code != 200:
            print("❌ Web应用未正常运行，请先启动 python app.py")
            return
        
        print("✅ Web应用运行正常，开始测试...\n")
        
        # 执行各项测试
        test_api_search()
        test_specific_cases()
        test_no_limits()
        test_data_table_functionality()
        
        print("\n" + "="*60)
        print("🎉 所有改进功能测试完成！")
        print("\n✅ 已实现的改进:")
        print("1. ✅ 按数据表分别搜索 - 支持按文件来源筛选")
        print("2. ✅ 移除搜索结果数量限制 - 返回所有匹配结果")
        print("3. ✅ 修复ID搜索问题 - ID匹配优先显示")
        print("4. ✅ 问题排查完成 - 搜索逻辑正常工作")
        
        print("\n🌟 新增功能:")
        print("- ID匹配结果优先排序")
        print("- 数据表选择器")
        print("- 匹配类型标识")
        print("- 无限制结果显示")
        
        print("\n🔗 Web界面: http://127.0.0.1:5000")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web应用，请确保运行 python app.py")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")


if __name__ == "__main__":
    main()
