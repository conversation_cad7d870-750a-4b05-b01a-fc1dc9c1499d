# -*- coding: utf-8 -*-
"""
深度调试搜索逻辑
"""

from database import IndexDatabase
from tokenizer import EconomicsTokenizer
from app import SearchEngine


def analyze_search_logic():
    """分析当前的搜索逻辑"""
    print("=== 分析当前搜索逻辑 ===")
    
    db = IndexDatabase()
    tokenizer = EconomicsTokenizer()
    
    # 1. 查看搜索词"volume"的分词结果
    print("1. 查询词'volume'的分词结果:")
    query_tokens = tokenizer.tokenize_description("volume")
    print(f"   分词结果: {query_tokens}")
    
    # 2. 直接在数据库中搜索"volume"
    print("\n2. 数据库中直接搜索'volume':")
    with db.get_connection() as conn:
        cursor = conn.execute('''
            SELECT wi.word, wi.word_type, wi.field_id, wi.source_text, f.description
            FROM word_index wi
            JOIN fields f ON wi.field_id = f.field_id
            WHERE wi.word = 'volume'
            ORDER BY wi.word_type, wi.field_id
            LIMIT 20
        ''')
        
        exact_matches = cursor.fetchall()
        print(f"   找到 {len(exact_matches)} 个精确匹配:")
        
        for word, word_type, field_id, source_text, description in exact_matches:
            print(f"     {word_type}: {field_id} -> '{word}' (来源: {source_text})")
            print(f"       描述: {description}")
            print()
    
    # 3. 查找包含"volume"的所有词汇
    print("3. 查找包含'volume'的所有词汇:")
    with db.get_connection() as conn:
        cursor = conn.execute('''
            SELECT DISTINCT word, word_type, COUNT(*) as count
            FROM word_index 
            WHERE word LIKE '%volume%'
            GROUP BY word, word_type
            ORDER BY word_type, word
        ''')
        
        like_matches = cursor.fetchall()
        print(f"   找到 {len(like_matches)} 个包含'volume'的词汇:")
        
        for word, word_type, count in like_matches:
            print(f"     {word_type}: '{word}' ({count} 次)")


def find_missing_volume_fields():
    """查找可能遗漏的包含volume的字段"""
    print("\n=== 查找遗漏的volume字段 ===")
    
    db = IndexDatabase()
    
    with db.get_connection() as conn:
        # 查找字段ID中包含volume但在索引中找不到volume词汇的字段
        cursor = conn.execute('''
            SELECT f.field_id, f.description, f.file_source
            FROM fields f
            WHERE (f.field_id LIKE '%volume%' OR f.description LIKE '%volume%')
            AND f.field_id NOT IN (
                SELECT DISTINCT field_id 
                FROM word_index 
                WHERE word = 'volume'
            )
            ORDER BY f.field_id
        ''')
        
        missing_fields = cursor.fetchall()
        print(f"可能遗漏的字段 ({len(missing_fields)} 个):")
        
        for field_id, description, file_source in missing_fields:
            print(f"  字段ID: {field_id}")
            print(f"  描述: {description}")
            print(f"  文件: {file_source}")
            
            # 检查这个字段的所有索引词汇
            cursor2 = conn.execute('''
                SELECT word, word_type, source_text
                FROM word_index 
                WHERE field_id = ?
                ORDER BY word_type, word
            ''', (field_id,))
            
            field_words = cursor2.fetchall()
            print(f"  索引词汇: {[f'{wt}:{w}' for w, wt, st in field_words]}")
            print()


def test_tokenizer_on_specific_ids():
    """测试分词器对特定ID的处理"""
    print("\n=== 测试分词器对特定ID的处理 ===")
    
    tokenizer = EconomicsTokenizer()
    
    # 从数据库中获取一些包含volume的字段ID
    db = IndexDatabase()
    with db.get_connection() as conn:
        cursor = conn.execute('''
            SELECT DISTINCT field_id 
            FROM fields 
            WHERE field_id LIKE '%volume%'
            ORDER BY field_id
            LIMIT 10
        ''')
        
        volume_ids = [row[0] for row in cursor.fetchall()]
    
    print("测试包含'volume'的字段ID分词:")
    for field_id in volume_ids:
        tokens = tokenizer.tokenize_field_id(field_id)
        has_volume = 'volume' in [t.lower() for t in tokens]
        print(f"  {field_id} -> {tokens} (包含volume: {has_volume})")


def debug_search_engine_logic():
    """调试搜索引擎的逻辑"""
    print("\n=== 调试搜索引擎逻辑 ===")
    
    search_engine = SearchEngine()
    
    # 手动执行搜索步骤
    query = "volume"
    print(f"搜索查询: '{query}'")
    
    # 1. 分词
    query_tokens = search_engine.tokenizer.tokenize_description(query)
    print(f"1. 查询分词: {query_tokens}")
    
    # 2. 对每个token搜索
    all_results = []
    for token in query_tokens:
        print(f"\n2. 搜索token '{token}':")
        results = search_engine.db.search_by_word(token)
        print(f"   找到 {len(results)} 个结果")
        
        # 显示前几个结果
        for i, result in enumerate(results[:5]):
            print(f"     {i+1}. {result['field_id']} ({result['word_type']}: {result['word']})")
        
        all_results.extend(results)
    
    print(f"\n3. 总共收集到 {len(all_results)} 个结果")
    
    # 3. 去重和排序逻辑
    unique_results = {}
    for result in all_results:
        key = (result['field_id'], result['region'], result['file_source'])
        if key not in unique_results:
            unique_results[key] = result
            unique_results[key]['match_count'] = 1
            unique_results[key]['matched_words'] = [result['word']]
            unique_results[key]['id_matches'] = 1 if result['word_type'] == 'id' else 0
            unique_results[key]['desc_matches'] = 1 if result['word_type'] == 'description' else 0
        else:
            unique_results[key]['match_count'] += 1
            if result['word'] not in unique_results[key]['matched_words']:
                unique_results[key]['matched_words'].append(result['word'])
            if result['word_type'] == 'id':
                unique_results[key]['id_matches'] += 1
            else:
                unique_results[key]['desc_matches'] += 1
    
    print(f"4. 去重后有 {len(unique_results)} 个唯一结果")
    
    # 4. 排序
    sorted_results = sorted(unique_results.values(), 
                          key=lambda x: (x['id_matches'], x['match_count'], len(x['matched_words'])), 
                          reverse=True)
    
    print(f"5. 排序后前10个结果:")
    for i, result in enumerate(sorted_results[:10]):
        print(f"   {i+1}. {result['field_id']} (ID匹配:{result['id_matches']}, 总匹配:{result['match_count']})")
        print(f"      匹配词汇: {result['matched_words']}")


def check_specific_volume_field():
    """检查特定的volume字段"""
    print("\n=== 检查特定volume字段 ===")
    
    # 手动检查一个应该能找到的字段
    test_field_id = "volume"  # 如果存在这样的字段
    
    db = IndexDatabase()
    tokenizer = EconomicsTokenizer()
    
    # 检查是否存在直接叫"volume"的字段
    with db.get_connection() as conn:
        cursor = conn.execute('''
            SELECT field_id, description, file_source
            FROM fields 
            WHERE field_id = 'volume'
        ''')
        
        direct_volume = cursor.fetchall()
        if direct_volume:
            print("找到直接叫'volume'的字段:")
            for field_id, description, file_source in direct_volume:
                print(f"  {field_id}: {description} ({file_source})")
        else:
            print("没有找到直接叫'volume'的字段")
        
        # 查找最简单的包含volume的字段
        cursor = conn.execute('''
            SELECT field_id, description, file_source
            FROM fields 
            WHERE field_id LIKE '%volume%'
            ORDER BY LENGTH(field_id)
            LIMIT 5
        ''')
        
        simple_volume_fields = cursor.fetchall()
        print(f"\n最简单的包含'volume'的字段:")
        for field_id, description, file_source in simple_volume_fields:
            print(f"  {field_id}: {description}")
            
            # 测试分词
            tokens = tokenizer.tokenize_field_id(field_id)
            print(f"    ID分词: {tokens}")
            
            # 检查索引
            cursor2 = conn.execute('''
                SELECT word, word_type 
                FROM word_index 
                WHERE field_id = ? AND word = 'volume'
            ''', (field_id,))
            
            volume_index = cursor2.fetchall()
            print(f"    volume索引: {volume_index}")
            print()


if __name__ == "__main__":
    print("开始深度调试搜索逻辑...")
    
    # 1. 分析搜索逻辑
    analyze_search_logic()
    
    # 2. 查找遗漏的字段
    find_missing_volume_fields()
    
    # 3. 测试分词器
    test_tokenizer_on_specific_ids()
    
    # 4. 调试搜索引擎
    debug_search_engine_logic()
    
    # 5. 检查特定字段
    check_specific_volume_field()
    
    print("\n调试完成！")
