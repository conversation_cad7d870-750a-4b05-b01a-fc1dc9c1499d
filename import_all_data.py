# -*- coding: utf-8 -*-
"""
导入所有CSV文件到索引数据库
"""

import os
import time
from data_processor import DataProcessor
from database import IndexDatabase


def check_current_status():
    """检查当前数据库状态"""
    print("=== 检查当前数据库状态 ===")
    
    processor = DataProcessor()
    db = IndexDatabase()
    
    # 获取所有CSV文件
    csv_files = processor.get_csv_files()
    print(f"发现 {len(csv_files)} 个CSV文件:")
    
    for i, file_path in enumerate(csv_files, 1):
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        print(f"  {i:2d}. {file_name} ({file_size:.1f} MB)")
    
    # 检查数据库当前状态
    stats = db.get_stats()
    print(f"\n当前数据库状态:")
    print(f"  总字段数: {stats['total_fields']}")
    print(f"  总词汇数: {stats['total_words']}")
    
    # 检查已处理的文件
    with db.get_connection() as conn:
        cursor = conn.execute('SELECT DISTINCT file_source FROM fields ORDER BY file_source')
        processed_files = [row[0] for row in cursor.fetchall()]
    
    print(f"\n已处理的文件 ({len(processed_files)} 个):")
    for file in processed_files:
        print(f"  - {file}")
    
    # 找出未处理的文件
    all_file_names = [os.path.basename(f) for f in csv_files]
    unprocessed_files = [f for f in all_file_names if f not in processed_files]
    
    print(f"\n未处理的文件 ({len(unprocessed_files)} 个):")
    for file in unprocessed_files:
        print(f"  - {file}")
    
    return csv_files, processed_files, unprocessed_files


def import_all_files():
    """导入所有CSV文件"""
    print("\n=== 开始导入所有CSV文件 ===")
    
    processor = DataProcessor()
    csv_files = processor.get_csv_files()
    
    print(f"准备处理 {len(csv_files)} 个CSV文件...")
    
    total_stats = {
        'total_files': len(csv_files),
        'processed_files': 0,
        'total_records': 0,
        'success_records': 0,
        'failed_records': 0,
        'file_details': [],
        'start_time': time.time()
    }
    
    for i, file_path in enumerate(csv_files, 1):
        file_name = os.path.basename(file_path)
        print(f"\n[{i}/{len(csv_files)}] 正在处理: {file_name}")
        
        try:
            # 记录开始时间
            file_start_time = time.time()
            
            # 处理文件
            file_stats = processor.process_csv_file(file_path)
            
            # 记录处理时间
            file_end_time = time.time()
            processing_time = file_end_time - file_start_time
            
            # 更新总统计
            total_stats['processed_files'] += 1
            total_stats['total_records'] += file_stats['total']
            total_stats['success_records'] += file_stats['success']
            total_stats['failed_records'] += file_stats['failed']
            
            # 记录文件详情
            file_detail = {
                'file': file_name,
                'stats': file_stats,
                'processing_time': processing_time,
                'records_per_second': file_stats['success'] / processing_time if processing_time > 0 else 0
            }
            total_stats['file_details'].append(file_detail)
            
            print(f"  完成: {file_stats['success']}/{file_stats['total']} 成功")
            print(f"  用时: {processing_time:.1f}秒 ({file_detail['records_per_second']:.0f} 记录/秒)")
            
        except Exception as e:
            print(f"  ❌ 处理文件 {file_name} 时发生错误: {e}")
            total_stats['file_details'].append({
                'file': file_name,
                'error': str(e)
            })
    
    # 计算总处理时间
    total_stats['end_time'] = time.time()
    total_stats['total_processing_time'] = total_stats['end_time'] - total_stats['start_time']
    
    return total_stats


def update_database_stats():
    """更新数据库统计信息"""
    print("\n=== 更新数据库统计信息 ===")
    
    db = IndexDatabase()
    
    print("正在更新类别统计...")
    db.update_category_stats()
    
    # 获取最新统计
    stats = db.get_stats()
    
    print("数据库统计信息已更新:")
    print(f"  总字段数: {stats['total_fields']}")
    print(f"  总词汇数: {stats['total_words']}")
    
    print(f"\n按类别分布:")
    for category, count in list(stats['by_category'].items())[:10]:
        print(f"  {category}: {count}")
    
    print(f"\n按地区分布:")
    for region, count in stats['by_region'].items():
        print(f"  {region}: {count}")
    
    return stats


def print_final_report(total_stats, final_stats):
    """打印最终报告"""
    print("\n" + "="*80)
    print("🎉 所有CSV文件导入完成！")
    print("="*80)
    
    # 处理统计
    print(f"\n📊 处理统计:")
    print(f"  总文件数: {total_stats['total_files']}")
    print(f"  成功处理: {total_stats['processed_files']}")
    print(f"  总记录数: {total_stats['total_records']:,}")
    print(f"  成功导入: {total_stats['success_records']:,}")
    print(f"  导入失败: {total_stats['failed_records']:,}")
    
    if total_stats['total_records'] > 0:
        success_rate = total_stats['success_records'] / total_stats['total_records'] * 100
        print(f"  成功率: {success_rate:.2f}%")
    
    # 性能统计
    total_time = total_stats['total_processing_time']
    print(f"\n⏱️ 性能统计:")
    print(f"  总处理时间: {total_time:.1f} 秒 ({total_time/60:.1f} 分钟)")
    
    if total_time > 0:
        avg_speed = total_stats['success_records'] / total_time
        print(f"  平均处理速度: {avg_speed:.0f} 记录/秒")
    
    # 数据库统计
    print(f"\n🗄️ 最终数据库状态:")
    print(f"  总字段数: {final_stats['total_fields']:,}")
    print(f"  总词汇数: {final_stats['total_words']:,}")
    print(f"  数据类别: {len(final_stats['by_category'])} 个")
    print(f"  覆盖地区: {len(final_stats['by_region'])} 个")
    
    # 文件处理详情
    print(f"\n📁 文件处理详情:")
    successful_files = [f for f in total_stats['file_details'] if 'error' not in f]
    failed_files = [f for f in total_stats['file_details'] if 'error' in f]
    
    print(f"  成功处理的文件 ({len(successful_files)} 个):")
    for detail in successful_files:
        stats = detail['stats']
        time_info = f"{detail['processing_time']:.1f}s"
        print(f"    ✅ {detail['file']}: {stats['success']}/{stats['total']} ({time_info})")
    
    if failed_files:
        print(f"\n  处理失败的文件 ({len(failed_files)} 个):")
        for detail in failed_files:
            print(f"    ❌ {detail['file']}: {detail['error']}")
    
    print(f"\n🚀 搜索引擎已准备就绪！")
    print(f"   Web界面: http://127.0.0.1:5000")
    print(f"   支持搜索 {final_stats['total_fields']:,} 个经济学数据字段")


def main():
    """主函数"""
    print("开始导入所有CSV文件到索引数据库...")
    
    # 1. 检查当前状态
    csv_files, processed_files, unprocessed_files = check_current_status()
    
    if not unprocessed_files:
        print("\n✅ 所有文件都已处理完成！")
        
        # 仍然更新统计信息
        final_stats = update_database_stats()
        
        print(f"\n当前数据库包含:")
        print(f"  📊 {final_stats['total_fields']:,} 个字段")
        print(f"  🔍 {final_stats['total_words']:,} 个索引词汇")
        print(f"  📁 {len(processed_files)} 个数据表")
        
        return
    
    # 2. 确认是否继续
    print(f"\n准备导入 {len(unprocessed_files)} 个新文件。")
    
    # 估算处理时间（基于已处理文件的平均速度）
    if processed_files:
        # 简单估算：假设平均每个文件需要30秒
        estimated_time = len(unprocessed_files) * 30
        print(f"预计需要时间: {estimated_time//60} 分钟 {estimated_time%60} 秒")
    
    response = input("是否继续？(y/N): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("取消导入。")
        return
    
    # 3. 导入所有文件
    total_stats = import_all_files()
    
    # 4. 更新数据库统计
    final_stats = update_database_stats()
    
    # 5. 打印最终报告
    print_final_report(total_stats, final_stats)


if __name__ == "__main__":
    main()
