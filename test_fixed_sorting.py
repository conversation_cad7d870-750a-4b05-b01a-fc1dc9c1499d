# -*- coding: utf-8 -*-
"""
测试修复后的排序功能
"""

from database import IndexDatabase
from app import SearchEngine


def test_fixed_sorting():
    """测试修复后的排序"""
    print("=== 测试修复后的排序 ===")
    
    db = IndexDatabase()
    
    # 测试数据库搜索的排序
    print("1. 测试数据库搜索排序:")
    results = db.search_by_word('volume', limit=20)
    
    print(f"前20个结果:")
    for i, result in enumerate(results, 1):
        match_type = "🔑ID匹配" if result['word_type'] == 'id' else "📝描述匹配"
        print(f"{i:2d}. {match_type} - {result['field_id']} - {result['description'][:40]}...")
    
    # 统计前20个结果中的匹配类型
    id_matches = sum(1 for r in results if r['word_type'] == 'id')
    desc_matches = sum(1 for r in results if r['word_type'] == 'description')
    
    print(f"\n前20个结果中:")
    print(f"ID匹配: {id_matches} 个")
    print(f"描述匹配: {desc_matches} 个")


def test_search_engine_with_fixed_sorting():
    """测试搜索引擎的修复后排序"""
    print("\n=== 测试搜索引擎的修复后排序 ===")
    
    search_engine = SearchEngine()
    
    # 搜索volume
    results = search_engine.search("volume")
    
    print(f"搜索引擎找到 {len(results)} 个结果")
    
    # 显示前20个结果
    print("\n前20个结果:")
    for i, result in enumerate(results[:20], 1):
        id_matches = result.get('id_matches', 0)
        desc_matches = result.get('desc_matches', 0)
        
        if id_matches > 0:
            match_type = "🔑ID匹配"
        else:
            match_type = "📝描述匹配"
        
        print(f"{i:2d}. {match_type} - {result['field_id']} - {result['description'][:40]}...")
    
    # 检查ID为volume的字段位置
    volume_positions = []
    for i, result in enumerate(results):
        if result['field_id'] == 'volume':
            volume_positions.append(i + 1)
    
    print(f"\nID为'volume'的字段在结果中的位置: {volume_positions}")
    
    # 统计前20个结果
    top_20 = results[:20]
    id_matches_top20 = sum(1 for r in top_20 if r.get('id_matches', 0) > 0)
    desc_matches_top20 = sum(1 for r in top_20 if r.get('desc_matches', 0) > 0 and r.get('id_matches', 0) == 0)
    
    print(f"\n前20个结果中:")
    print(f"ID匹配: {id_matches_top20} 个")
    print(f"纯描述匹配: {desc_matches_top20} 个")


def test_specific_volume_search():
    """测试特定的volume搜索"""
    print("\n=== 测试特定的volume搜索 ===")
    
    search_engine = SearchEngine()
    
    # 搜索volume
    results = search_engine.search("volume")
    
    # 查找ID为volume的结果
    volume_results = [r for r in results if r['field_id'] == 'volume']
    
    print(f"找到 {len(volume_results)} 个ID为'volume'的字段:")
    
    for i, result in enumerate(volume_results, 1):
        # 找到这个结果在总结果中的位置
        position = next((j for j, r in enumerate(results) if 
                        r['field_id'] == result['field_id'] and 
                        r['region'] == result['region'] and
                        r['file_source'] == result['file_source']), -1) + 1
        
        print(f"{i}. 字段ID: {result['field_id']}")
        print(f"   描述: {result['description']}")
        print(f"   地区: {result['region']}")
        print(f"   文件: {result['file_source']}")
        print(f"   在搜索结果中的位置: 第 {position} 位")
        print(f"   ID匹配: {result.get('id_matches', 0)}")
        print(f"   描述匹配: {result.get('desc_matches', 0)}")
        print()


def test_web_api():
    """测试Web API的结果"""
    print("\n=== 测试Web API的结果 ===")
    
    try:
        import requests
        
        response = requests.get("http://127.0.0.1:5000/api/search?q=volume", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"API返回 {data['total_results']} 个结果")
            
            # 查找ID为volume的结果
            volume_results = [r for r in data['results'] if r['field_id'] == 'volume']
            
            print(f"ID为'volume'的结果: {len(volume_results)} 个")
            
            if volume_results:
                print("\nID为'volume'的结果在API中的位置:")
                for result in volume_results:
                    position = next((i for i, r in enumerate(data['results']) if 
                                   r['field_id'] == result['field_id'] and 
                                   r['region'] == result['region']), -1) + 1
                    print(f"  {result['field_id']} ({result['region']}): 第 {position} 位")
            
            # 显示前10个结果
            print("\nAPI前10个结果:")
            for i, result in enumerate(data['results'][:10], 1):
                match_type = "🔑ID匹配" if result.get('id_matches', 0) > 0 else "📝描述匹配"
                print(f"{i:2d}. {match_type} - {result['field_id']} - {result['description'][:40]}...")
        
        else:
            print(f"API请求失败: {response.status_code}")
    
    except Exception as e:
        print(f"无法测试API: {e}")


if __name__ == "__main__":
    print("开始测试修复后的排序功能...")
    
    # 1. 测试数据库排序
    test_fixed_sorting()
    
    # 2. 测试搜索引擎排序
    test_search_engine_with_fixed_sorting()
    
    # 3. 测试特定volume搜索
    test_specific_volume_search()
    
    # 4. 测试Web API
    test_web_api()
    
    print("\n🎉 排序修复测试完成！")
