# -*- coding: utf-8 -*-
"""
搜索功能测试脚本
"""

from database import IndexDatabase
from tokenizer import EconomicsTokenizer


class SearchEngine:
    """简单的搜索引擎"""
    
    def __init__(self):
        self.db = IndexDatabase()
        self.tokenizer = EconomicsTokenizer()
    
    def search(self, query: str, category: str = None, region: str = None, limit: int = 20):
        """搜索字段"""
        # 对查询进行分词
        query_tokens = self.tokenizer.tokenize_description(query)
        
        if not query_tokens:
            return []
        
        # 搜索每个词汇
        all_results = []
        for token in query_tokens:
            results = self.db.search_by_word(token, category, region, limit)
            all_results.extend(results)
        
        # 去重并按相关性排序（简单实现）
        unique_results = {}
        for result in all_results:
            key = (result['field_id'], result['region'])
            if key not in unique_results:
                unique_results[key] = result
                unique_results[key]['match_count'] = 1
            else:
                unique_results[key]['match_count'] += 1
        
        # 按匹配次数排序
        sorted_results = sorted(unique_results.values(), 
                              key=lambda x: x['match_count'], 
                              reverse=True)
        
        return sorted_results[:limit]
    
    def get_categories(self):
        """获取所有类别"""
        return self.db.get_categories()
    
    def get_regions(self):
        """获取所有地区"""
        return self.db.get_regions()


def test_search():
    """测试搜索功能"""
    print("=== 搜索功能测试 ===")
    
    search_engine = SearchEngine()
    
    # 获取可用的类别和地区
    categories = search_engine.get_categories()
    regions = search_engine.get_regions()
    
    print(f"可用类别: {categories}")
    print(f"可用地区: {regions}")
    
    # 测试搜索查询
    test_queries = [
        "volume",
        "price",
        "market capitalization",
        "beta",
        "return",
        "assets",
        "ratio",
        "turnover"
    ]
    
    for query in test_queries:
        print(f"\n--- 搜索: '{query}' ---")
        results = search_engine.search(query, limit=5)
        
        if not results:
            print("未找到结果")
            continue
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['field_id']} ({result['region']})")
            print(f"   描述: {result['description']}")
            print(f"   类别: {result['category_name']}")
            print(f"   匹配度: {result['match_count']}")
            print()


def test_category_search():
    """测试按类别搜索"""
    print("\n=== 按类别搜索测试 ===")
    
    search_engine = SearchEngine()
    
    # 测试不同类别的搜索
    test_cases = [
        ("price", "Price Volume"),
        ("assets", "Fundamental"),
        ("beta", "Model"),
        ("news", "News")
    ]
    
    for query, category in test_cases:
        print(f"\n--- 在类别 '{category}' 中搜索: '{query}' ---")
        results = search_engine.search(query, category=category, limit=3)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['field_id']} - {result['description'][:50]}...")


def test_region_search():
    """测试按地区搜索"""
    print("\n=== 按地区搜索测试 ===")
    
    search_engine = SearchEngine()
    
    # 测试不同地区的搜索
    regions = ["USA", "CHN", "EUR"]
    
    for region in regions:
        print(f"\n--- 在地区 '{region}' 中搜索: 'volume' ---")
        results = search_engine.search("volume", region=region, limit=3)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['field_id']} - {result['description'][:50]}...")


def interactive_search():
    """交互式搜索"""
    print("\n=== 交互式搜索 ===")
    print("输入搜索查询（输入 'quit' 退出）:")
    
    search_engine = SearchEngine()
    
    while True:
        query = input("\n搜索> ").strip()
        
        if query.lower() in ['quit', 'exit', 'q']:
            break
        
        if not query:
            continue
        
        results = search_engine.search(query, limit=10)
        
        if not results:
            print("未找到结果")
            continue
        
        print(f"\n找到 {len(results)} 个结果:")
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result['field_id']} ({result['region']})")
            print(f"   描述: {result['description']}")
            print(f"   类别: {result['category_name']}")
            print(f"   数据集: {result['dataset_name']}")


if __name__ == "__main__":
    # 检查数据库是否存在
    db = IndexDatabase()
    stats = db.get_stats()
    
    if stats['total_fields'] == 0:
        print("数据库为空，请先运行 build_index.py 构建索引")
        exit(1)
    
    print(f"数据库包含 {stats['total_fields']} 个字段，{stats['total_words']} 个词汇")
    
    # 运行测试
    test_search()
    test_category_search()
    test_region_search()
    
    # 交互式搜索
    interactive_search()
