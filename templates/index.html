<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经济学数据字段搜索引擎</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        
        .search-section {
            padding: 40px;
        }
        
        .search-form {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .filter-select {
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            background: white;
            min-width: 150px;
        }
        
        .search-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
        }
        
        .results-section {
            margin-top: 30px;
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        
        .result-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .result-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .field-id {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }
        
        .badges {
            display: flex;
            gap: 8px;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .badge-region {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .badge-category {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .badge-match {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .description {
            color: #555;
            line-height: 1.6;
            margin: 10px 0;
        }
        
        .dataset {
            color: #666;
            font-size: 0.9em;
            font-style: italic;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .no-results {
            text-align: center;
            padding: 40px;
            color: #999;
        }
        
        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }
            
            .search-input {
                min-width: auto;
            }
            
            .stats {
                flex-direction: column;
                gap: 15px;
            }
            
            .result-header {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>经济学数据字段搜索引擎</h1>
            <p>智能双语模糊搜索，支持中英文查询</p>
            
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">{{ stats.total_fields }}</span>
                    <span>总字段数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ stats.total_words }}</span>
                    <span>索引词汇</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ categories|length }}</span>
                    <span>数据类别</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ regions|length }}</span>
                    <span>覆盖地区</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ file_sources|length }}</span>
                    <span>数据表</span>
                </div>
            </div>
        </div>
        
        <div class="search-section">
            <form class="search-form" onsubmit="performSearch(event)">
                <input type="text" 
                       class="search-input" 
                       id="searchQuery" 
                       placeholder="输入搜索关键词，如：market cap, volume, 市值, 成交量..."
                       required>
                
                <select class="filter-select" id="categoryFilter">
                    <option value="">所有类别</option>
                    {% for category in categories %}
                    <option value="{{ category }}">{{ category }}</option>
                    {% endfor %}
                </select>
                
                <select class="filter-select" id="regionFilter">
                    <option value="">所有地区</option>
                    {% for region in regions %}
                    <option value="{{ region }}">{{ region }}</option>
                    {% endfor %}
                </select>

                <select class="filter-select" id="fileSourceFilter">
                    <option value="">所有数据表</option>
                    {% for file_source in file_sources %}
                    <option value="{{ file_source }}">{{ file_source }}</option>
                    {% endfor %}
                </select>

                <button type="submit" class="search-btn">搜索</button>
            </form>
            
            <div class="results-section" id="resultsSection" style="display: none;">
                <div class="results-header">
                    <h3 id="resultsTitle">搜索结果</h3>
                    <span id="resultsCount"></span>
                </div>
                <div id="resultsContainer"></div>
            </div>
        </div>
    </div>

    <script>
        async function performSearch(event) {
            event.preventDefault();

            const query = document.getElementById('searchQuery').value.trim();
            const category = document.getElementById('categoryFilter').value;
            const region = document.getElementById('regionFilter').value;
            const fileSource = document.getElementById('fileSourceFilter').value;

            if (!query) {
                alert('请输入搜索关键词');
                return;
            }

            // 显示加载状态
            const resultsSection = document.getElementById('resultsSection');
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsTitle = document.getElementById('resultsTitle');
            const resultsCount = document.getElementById('resultsCount');

            resultsSection.style.display = 'block';
            resultsContainer.innerHTML = '<div class="loading">正在搜索...</div>';

            try {
                // 构建查询参数
                const params = new URLSearchParams({
                    q: query
                });

                if (category) params.append('category', category);
                if (region) params.append('region', region);
                if (fileSource) params.append('file_source', fileSource);

                // 发送搜索请求
                const response = await fetch(`/api/search?${params}`);
                const data = await response.json();
                
                // 更新结果标题
                resultsTitle.textContent = `"${query}" 的搜索结果`;
                resultsCount.textContent = `找到 ${data.total_results} 个结果`;
                
                // 显示结果
                if (data.results.length === 0) {
                    resultsContainer.innerHTML = '<div class="no-results">未找到匹配的结果，请尝试其他关键词</div>';
                } else {
                    resultsContainer.innerHTML = data.results.map(result => `
                        <div class="result-item">
                            <div class="result-header">
                                <div class="field-id">${result.field_id}</div>
                                <div class="badges">
                                    <span class="badge badge-region">${result.region}</span>
                                    <span class="badge badge-category">${result.category_name}</span>
                                    ${result.id_matches > 0 ? '<span class="badge" style="background: #ffeb3b; color: #f57f17;">ID匹配</span>' : ''}
                                    <span class="badge badge-match">匹配度: ${result.match_count}</span>
                                </div>
                            </div>
                            <div class="description">${result.description}</div>
                            <div class="dataset">数据表: ${result.file_source} | 数据集: ${result.dataset_name}</div>
                        </div>
                    `).join('');
                }
                
            } catch (error) {
                console.error('搜索错误:', error);
                resultsContainer.innerHTML = '<div class="no-results">搜索出错，请稍后重试</div>';
            }
        }
        
        // 回车键搜索
        document.getElementById('searchQuery').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                performSearch(event);
            }
        });
    </script>
</body>
</html>
