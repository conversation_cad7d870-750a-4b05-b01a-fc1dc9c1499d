# -*- coding: utf-8 -*-
"""
数据处理模块
负责读取CSV文件，处理数据并构建索引
"""

import pandas as pd
import os
import glob
from typing import List, Dict, Generator
from database import IndexDatabase
from tokenizer import EconomicsTokenizer
from config import DATA_CONFIG


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.db = IndexDatabase()
        self.tokenizer = EconomicsTokenizer()
        self.csv_folder = DATA_CONFIG['csv_folder']
    
    def get_csv_files(self) -> List[str]:
        """获取所有CSV文件路径"""
        pattern = os.path.join(self.csv_folder, '*.csv')
        return glob.glob(pattern)
    
    def read_csv_file(self, file_path: str) -> pd.DataFrame:
        """读取CSV文件"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            # 添加文件来源信息
            df['file_source'] = os.path.basename(file_path)
            return df
        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {e}")
            return pd.DataFrame()
    
    def extract_field_data(self, row: pd.Series) -> Dict:
        """从DataFrame行中提取字段数据"""
        return {
            'field_id': str(row.get('id', '')),
            'description': str(row.get('description', '')),
            'region': str(row.get('region', '')),
            'universe': str(row.get('universe', '')),
            'field_type': str(row.get('type', '')),
            'coverage': float(row.get('coverage', 0.0)) if pd.notna(row.get('coverage')) else 0.0,
            'dataset_id': str(row.get('dataset.id', '')),
            'dataset_name': str(row.get('dataset.name', '')),
            'category_id': str(row.get('category.id', '')),
            'category_name': str(row.get('category.name', '')),
            'subcategory_id': str(row.get('subcategory.id', '')),
            'subcategory_name': str(row.get('subcategory.name', '')),
            'file_source': str(row.get('file_source', ''))
        }
    
    def process_single_field(self, field_data: Dict) -> bool:
        """处理单个字段数据"""
        try:
            # 插入字段基本信息
            self.db.insert_field(field_data)
            
            # 进行分词处理
            tokens = self.tokenizer.tokenize_field(
                field_data['field_id'], 
                field_data['description']
            )
            
            # 插入ID分词索引
            for i, token in enumerate(tokens['id_tokens']):
                self.db.insert_word_index(
                    word=token,
                    word_type='id',
                    field_id=field_data['field_id'],
                    position=i,
                    source_text=field_data['field_id']
                )
            
            # 插入描述分词索引
            for i, token in enumerate(tokens['description_tokens']):
                self.db.insert_word_index(
                    word=token,
                    word_type='description',
                    field_id=field_data['field_id'],
                    position=i,
                    source_text=field_data['description']
                )
            
            return True
            
        except Exception as e:
            print(f"处理字段 {field_data.get('field_id', 'unknown')} 时出错: {e}")
            return False
    
    def process_csv_file(self, file_path: str) -> Dict[str, int]:
        """处理单个CSV文件"""
        print(f"正在处理文件: {os.path.basename(file_path)}")
        
        df = self.read_csv_file(file_path)
        if df.empty:
            return {'total': 0, 'success': 0, 'failed': 0}
        
        stats = {'total': len(df), 'success': 0, 'failed': 0}
        
        for _, row in df.iterrows():
            field_data = self.extract_field_data(row)
            
            # 跳过无效数据
            if not field_data['field_id'] or field_data['field_id'] == 'nan':
                stats['failed'] += 1
                continue
            
            if self.process_single_field(field_data):
                stats['success'] += 1
            else:
                stats['failed'] += 1
        
        print(f"文件处理完成: 总计 {stats['total']}, 成功 {stats['success']}, 失败 {stats['failed']}")
        return stats
    
    def process_all_files(self) -> Dict[str, any]:
        """处理所有CSV文件"""
        csv_files = self.get_csv_files()
        if not csv_files:
            print(f"在目录 {self.csv_folder} 中未找到CSV文件")
            return {'total_files': 0, 'processed_files': 0, 'total_records': 0}
        
        print(f"找到 {len(csv_files)} 个CSV文件")
        
        total_stats = {
            'total_files': len(csv_files),
            'processed_files': 0,
            'total_records': 0,
            'success_records': 0,
            'failed_records': 0,
            'file_details': []
        }
        
        for file_path in csv_files:
            try:
                file_stats = self.process_csv_file(file_path)
                total_stats['processed_files'] += 1
                total_stats['total_records'] += file_stats['total']
                total_stats['success_records'] += file_stats['success']
                total_stats['failed_records'] += file_stats['failed']
                
                total_stats['file_details'].append({
                    'file': os.path.basename(file_path),
                    'stats': file_stats
                })
                
            except Exception as e:
                print(f"处理文件 {file_path} 时发生错误: {e}")
                total_stats['file_details'].append({
                    'file': os.path.basename(file_path),
                    'error': str(e)
                })
        
        # 更新类别统计
        print("正在更新类别统计...")
        self.db.update_category_stats()
        
        return total_stats
    
    def get_sample_data(self, limit: int = 10) -> List[Dict]:
        """获取样本数据用于测试"""
        csv_files = self.get_csv_files()
        if not csv_files:
            return []
        
        # 读取第一个文件的前几行
        df = self.read_csv_file(csv_files[0])
        if df.empty:
            return []
        
        sample_data = []
        for _, row in df.head(limit).iterrows():
            field_data = self.extract_field_data(row)
            if field_data['field_id'] and field_data['field_id'] != 'nan':
                # 添加分词结果
                tokens = self.tokenizer.tokenize_field(
                    field_data['field_id'], 
                    field_data['description']
                )
                field_data['tokens'] = tokens
                sample_data.append(field_data)
        
        return sample_data
    
    def analyze_data_distribution(self) -> Dict:
        """分析数据分布"""
        csv_files = self.get_csv_files()
        distribution = {
            'files': [],
            'categories': {},
            'regions': {},
            'total_fields': 0
        }
        
        for file_path in csv_files:
            df = self.read_csv_file(file_path)
            if df.empty:
                continue
            
            file_info = {
                'file': os.path.basename(file_path),
                'records': len(df),
                'categories': df['category.name'].value_counts().to_dict() if 'category.name' in df.columns else {},
                'regions': df['region'].value_counts().to_dict() if 'region' in df.columns else {}
            }
            
            distribution['files'].append(file_info)
            distribution['total_fields'] += len(df)
            
            # 汇总类别统计
            if 'category.name' in df.columns:
                for cat, count in df['category.name'].value_counts().items():
                    distribution['categories'][cat] = distribution['categories'].get(cat, 0) + count
            
            # 汇总地区统计
            if 'region' in df.columns:
                for region, count in df['region'].value_counts().items():
                    distribution['regions'][region] = distribution['regions'].get(region, 0) + count
        
        return distribution
