# -*- coding: utf-8 -*-
"""
构建索引数据库脚本
"""

import time
from data_processor import DataProcessor
from database import IndexDatabase


def main():
    """主函数"""
    print("=== 经济学数据字段搜索引擎 - 索引构建 ===")
    print("开始构建词汇索引数据库...")
    
    start_time = time.time()
    
    # 创建数据处理器
    processor = DataProcessor()
    
    # 处理所有文件
    print("正在处理所有CSV文件...")
    results = processor.process_all_files()
    
    # 显示处理结果
    print("\n=== 处理结果 ===")
    print(f"总文件数: {results['total_files']}")
    print(f"已处理文件数: {results['processed_files']}")
    print(f"总记录数: {results['total_records']}")
    print(f"成功处理: {results['success_records']}")
    print(f"处理失败: {results['failed_records']}")
    
    if results['failed_records'] > 0:
        print(f"成功率: {results['success_records'] / results['total_records'] * 100:.2f}%")
    
    # 显示详细统计
    print("\n=== 文件处理详情 ===")
    for detail in results['file_details'][:10]:  # 只显示前10个文件
        if 'error' in detail:
            print(f"❌ {detail['file']}: {detail['error']}")
        else:
            stats = detail['stats']
            print(f"✅ {detail['file']}: {stats['success']}/{stats['total']} 成功")
    
    if len(results['file_details']) > 10:
        print(f"... 还有 {len(results['file_details']) - 10} 个文件")
    
    # 获取数据库统计
    db = IndexDatabase()
    db_stats = db.get_stats()
    
    print("\n=== 数据库统计 ===")
    print(f"总字段数: {db_stats['total_fields']}")
    print(f"总词汇数: {db_stats['total_words']}")
    
    print("\n按类别分布:")
    for category, count in list(db_stats['by_category'].items())[:10]:
        print(f"  {category}: {count}")
    
    print("\n按地区分布:")
    for region, count in db_stats['by_region'].items():
        print(f"  {region}: {count}")
    
    # 计算处理时间
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n=== 完成 ===")
    print(f"总处理时间: {processing_time:.2f} 秒")
    print(f"平均处理速度: {results['success_records'] / processing_time:.2f} 记录/秒")
    
    print("\n索引数据库构建完成！")
    print("现在可以使用搜索功能了。")


if __name__ == "__main__":
    main()
