# -*- coding: utf-8 -*-
"""
检查导入进度的脚本
"""

from database import IndexDatabase
import os
import glob


def check_progress():
    """检查当前导入进度"""
    print("=== 数据导入进度检查 ===")
    
    db = IndexDatabase()
    
    # 获取当前数据库统计
    stats = db.get_stats()
    
    print(f"📊 当前数据库状态:")
    print(f"  总字段数: {stats['total_fields']:,}")
    print(f"  总词汇数: {stats['total_words']:,}")
    
    # 获取已处理的文件
    with db.get_connection() as conn:
        cursor = conn.execute('''
            SELECT file_source, COUNT(*) as record_count
            FROM fields 
            GROUP BY file_source 
            ORDER BY file_source
        ''')
        processed_files = cursor.fetchall()
    
    print(f"\n📁 已处理的文件 ({len(processed_files)} 个):")
    total_processed_records = 0
    for file_source, count in processed_files:
        print(f"  ✅ {file_source}: {count:,} 条记录")
        total_processed_records += count
    
    print(f"\n📈 处理统计:")
    print(f"  已处理记录总数: {total_processed_records:,}")
    
    # 获取所有CSV文件
    csv_folder = "split_files"
    pattern = os.path.join(csv_folder, '*.csv')
    all_csv_files = glob.glob(pattern)
    
    processed_file_names = [file_source for file_source, _ in processed_files]
    all_file_names = [os.path.basename(f) for f in all_csv_files]
    
    unprocessed_files = [f for f in all_file_names if f not in processed_file_names]
    
    print(f"\n⏳ 待处理文件 ({len(unprocessed_files)} 个):")
    for file in unprocessed_files:
        file_path = os.path.join(csv_folder, file)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            print(f"  ⏸️ {file} ({file_size:.1f} MB)")
    
    # 计算进度百分比
    progress_percentage = len(processed_files) / len(all_csv_files) * 100
    print(f"\n🎯 总体进度: {len(processed_files)}/{len(all_csv_files)} 文件 ({progress_percentage:.1f}%)")
    
    # 按类别统计
    print(f"\n📊 按类别分布:")
    for category, count in list(stats['by_category'].items())[:10]:
        print(f"  {category}: {count:,}")
    
    # 按地区统计
    print(f"\n🌍 按地区分布:")
    for region, count in stats['by_region'].items():
        print(f"  {region}: {count:,}")


if __name__ == "__main__":
    check_progress()
