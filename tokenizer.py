# -*- coding: utf-8 -*-
"""
分词处理模块
负责对字段ID和描述进行分词处理
"""

import jieba
import re
import os
from typing import List, <PERSON><PERSON>, Dict
from config import TOKENIZER_CONFIG, ECONOMICS_TERMS, DATA_CONFIG


class EconomicsTokenizer:
    """经济学专业分词器"""
    
    def __init__(self):
        self.setup_jieba()
        self.load_economics_dict()
    
    def setup_jieba(self):
        """配置jieba分词器"""
        # 设置jieba模式
        if TOKENIZER_CONFIG.get('enable_paddle', False):
            jieba.enable_paddle()
        
        # 加载自定义词典
        dict_path = TOKENIZER_CONFIG.get('jieba_dict_path')
        if dict_path and os.path.exists(dict_path):
            jieba.load_userdict(dict_path)
    
    def load_economics_dict(self):
        """加载经济学专业词汇"""
        # 添加经济学专业术语到jieba词典
        for term in ECONOMICS_TERMS:
            jieba.add_word(term, freq=1000)  # 设置较高频率确保被识别
        
        # 创建经济学词典文件（如果不存在）
        dict_path = DATA_CONFIG['economics_dict_path']
        if not os.path.exists(dict_path):
            self.create_economics_dict_file(dict_path)
    
    def create_economics_dict_file(self, dict_path: str):
        """创建经济学词典文件"""
        os.makedirs(os.path.dirname(dict_path), exist_ok=True)
        with open(dict_path, 'w', encoding='utf-8') as f:
            for term in ECONOMICS_TERMS:
                f.write(f"{term} 1000 n\n")  # 格式：词汇 频率 词性
    
    def tokenize_field_id(self, field_id: str) -> List[str]:
        """
        对字段ID进行分词
        主要基于下划线分割，同时处理数字和字母的组合
        """
        if not field_id:
            return []
        
        # 基于下划线分割
        parts = field_id.split('_')
        tokens = []
        
        for part in parts:
            if not part:
                continue
            
            # 处理数字和字母混合的情况，如 "fnd27" -> ["fnd", "27"]
            sub_tokens = self._split_alphanumeric(part)
            tokens.extend(sub_tokens)
        
        # 过滤掉过短的token
        tokens = [token for token in tokens if len(token) >= 2]
        
        return tokens
    
    def _split_alphanumeric(self, text: str) -> List[str]:
        """分割字母数字混合的字符串"""
        # 使用正则表达式分割字母和数字
        pattern = r'([a-zA-Z]+)(\d+)|(\d+)([a-zA-Z]+)|([a-zA-Z]+)|(\d+)'
        matches = re.findall(pattern, text)

        tokens = []
        for match in matches:
            for group in match:
                if group:
                    # 对纯字母部分进行进一步的复合词分割
                    if group.isalpha() and len(group) > 4:
                        sub_tokens = self._split_compound_word(group)
                        tokens.extend(sub_tokens)
                    else:
                        tokens.append(group)

        return tokens if tokens else [text]

    def _split_compound_word(self, word: str) -> List[str]:
        """分割复合词，特别是经济学术语"""
        word_lower = word.lower()

        # 定义常见的经济学词汇片段
        common_fragments = [
            'volume', 'price', 'market', 'trade', 'stock', 'share', 'asset', 'equity',
            'debt', 'ratio', 'rate', 'return', 'yield', 'beta', 'alpha', 'risk',
            'value', 'cap', 'flow', 'cash', 'profit', 'revenue', 'sales', 'cost',
            'avg', 'average', 'mean', 'median', 'std', 'stddev', 'var', 'variance',
            'max', 'min', 'high', 'low', 'open', 'close', 'adj', 'adjusted',
            'daily', 'weekly', 'monthly', 'annual', 'total', 'net', 'gross',
            'count', 'counts', 'num', 'number', 'size', 'weight', 'score',
            'index', 'factor', 'model', 'signal', 'trend', 'momentum'
        ]

        # 按长度降序排列，优先匹配长词汇
        common_fragments.sort(key=len, reverse=True)

        tokens = []
        remaining = word_lower

        while remaining:
            matched = False
            for fragment in common_fragments:
                if remaining.startswith(fragment):
                    tokens.append(fragment)
                    remaining = remaining[len(fragment):]
                    matched = True
                    break

            if not matched:
                # 如果没有匹配到已知片段，尝试从末尾匹配
                for fragment in common_fragments:
                    if remaining.endswith(fragment) and len(remaining) > len(fragment):
                        # 将前面的部分作为一个token
                        prefix = remaining[:-len(fragment)]
                        if len(prefix) >= 2:  # 只保留长度>=2的前缀
                            tokens.append(prefix)
                        tokens.append(fragment)
                        remaining = ""
                        matched = True
                        break

                if not matched:
                    # 如果还是没有匹配，返回原词
                    tokens.append(remaining)
                    break

        # 如果分割结果只有一个token且等于原词，返回原词
        if len(tokens) == 1 and tokens[0] == word_lower:
            return [word]

        return tokens
    
    def tokenize_description(self, description: str) -> List[str]:
        """
        对字段描述进行分词
        处理中英文混合文本
        """
        if not description:
            return []
        
        # 预处理：清理文本
        cleaned_text = self._clean_text(description)
        
        # 分离中英文进行不同处理
        segments = self._segment_text_by_language(cleaned_text)
        
        all_tokens = []
        for segment, lang in segments:
            if lang == 'chinese':
                tokens = self._tokenize_chinese(segment)
            elif lang == 'english':
                tokens = self._tokenize_english(segment)
            else:
                tokens = [segment] if segment.strip() else []
            
            all_tokens.extend(tokens)
        
        # 过滤和清理tokens
        tokens = self._filter_tokens(all_tokens)
        
        return tokens
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除多余的空格和特殊字符
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[^\w\s\u4e00-\u9fff\-\(\)\,\.]', ' ', text)
        return text.strip()
    
    def _segment_text_by_language(self, text: str) -> List[Tuple[str, str]]:
        """按语言分割文本"""
        segments = []
        current_segment = ""
        current_lang = None
        
        for char in text:
            if '\u4e00' <= char <= '\u9fff':  # 中文字符
                if current_lang != 'chinese':
                    if current_segment.strip():
                        segments.append((current_segment.strip(), current_lang))
                    current_segment = char
                    current_lang = 'chinese'
                else:
                    current_segment += char
            elif char.isalpha():  # 英文字符
                if current_lang != 'english':
                    if current_segment.strip():
                        segments.append((current_segment.strip(), current_lang))
                    current_segment = char
                    current_lang = 'english'
                else:
                    current_segment += char
            else:  # 其他字符（数字、标点等）
                current_segment += char
        
        if current_segment.strip():
            segments.append((current_segment.strip(), current_lang))
        
        return segments
    
    def _tokenize_chinese(self, text: str) -> List[str]:
        """中文分词"""
        tokens = jieba.lcut(text, cut_all=TOKENIZER_CONFIG.get('cut_all', False),
                           HMM=TOKENIZER_CONFIG.get('hmm', True))
        return [token.strip() for token in tokens if token.strip()]
    
    def _tokenize_english(self, text: str) -> List[str]:
        """英文分词"""
        # 简单的英文分词：按空格和标点分割
        tokens = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        return tokens
    
    def _filter_tokens(self, tokens: List[str]) -> List[str]:
        """过滤tokens"""
        filtered = []
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
                     '的', '了', '在', '是', '有', '和', '与', '或', '但', '等', '及'}
        
        for token in tokens:
            token = token.strip()
            if (len(token) >= 2 and 
                token.lower() not in stop_words and
                not token.isdigit() and
                not re.match(r'^[^\w\u4e00-\u9fff]+$', token)):  # 不是纯标点
                filtered.append(token)
        
        return filtered
    
    def tokenize_field(self, field_id: str, description: str) -> Dict[str, List[str]]:
        """
        对字段进行完整分词
        返回ID分词和描述分词的结果
        """
        return {
            'id_tokens': self.tokenize_field_id(field_id),
            'description_tokens': self.tokenize_description(description)
        }
