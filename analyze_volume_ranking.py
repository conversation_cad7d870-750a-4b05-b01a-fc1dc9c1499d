# -*- coding: utf-8 -*-
"""
分析volume字段排名的原因
"""

from database import IndexDatabase
from app import SearchEngine


def analyze_volume_ranking():
    """分析volume字段的排名"""
    print("=== 分析volume字段排名 ===")
    
    search_engine = SearchEngine()
    
    # 搜索volume
    results = search_engine.search("volume")
    
    # 找到ID为volume的字段
    volume_results = [r for r in results if r['field_id'] == 'volume']
    
    if volume_results:
        volume_result = volume_results[0]  # 取第一个
        
        print(f"ID为'volume'的字段信息:")
        print(f"  字段ID: {volume_result['field_id']}")
        print(f"  描述: {volume_result['description']}")
        print(f"  类别: {volume_result['category_name']}")
        print(f"  ID匹配: {volume_result.get('id_matches', 0)}")
        print(f"  描述匹配: {volume_result.get('desc_matches', 0)}")
        print(f"  总匹配: {volume_result.get('match_count', 0)}")
        print(f"  匹配词汇: {volume_result.get('matched_words', [])}")
    
    # 分析前40个结果，看看为什么它们排在volume前面
    print(f"\n前40个结果的分析:")
    
    for i, result in enumerate(results[:40], 1):
        if result['field_id'] == 'volume':
            print(f"{i:2d}. 🎯 {result['field_id']} - 类别:{result['category_name']} - 匹配:{result.get('match_count', 0)}")
        else:
            print(f"{i:2d}. {result['field_id']} - 类别:{result['category_name']} - 匹配:{result.get('match_count', 0)}")


def check_sorting_logic():
    """检查排序逻辑"""
    print("\n=== 检查排序逻辑 ===")
    
    db = IndexDatabase()
    
    # 直接查询volume相关的ID匹配，看排序
    with db.get_connection() as conn:
        cursor = conn.execute('''
            SELECT DISTINCT f.field_id, f.category_name, f.description,
                   wi.word, wi.word_type, wi.source_text,
                   CASE WHEN wi.word_type = 'id' THEN 0 ELSE 1 END as sort_priority
            FROM fields f
            JOIN word_index wi ON f.field_id = wi.field_id
            WHERE wi.word = 'volume' AND wi.word_type = 'id'
            ORDER BY CASE WHEN wi.word_type = 'id' THEN 0 ELSE 1 END, f.category_name, f.field_id
            LIMIT 50
        ''')
        
        results = cursor.fetchall()
        
        print(f"ID类型的volume匹配结果 (按排序规则):")
        for i, row in enumerate(results, 1):
            field_id, category_name, description, word, word_type, source_text, sort_priority = row
            print(f"{i:2d}. {field_id} - 类别:{category_name} - 优先级:{sort_priority}")


def check_search_engine_logic():
    """检查搜索引擎的逻辑"""
    print("\n=== 检查搜索引擎逻辑 ===")
    
    search_engine = SearchEngine()
    
    # 模拟搜索引擎的处理过程
    query = "volume"
    query_tokens = search_engine.tokenizer.tokenize_description(query)
    
    print(f"查询词: {query}")
    print(f"分词结果: {query_tokens}")
    
    # 对每个token搜索
    all_results = []
    for token in query_tokens:
        results = search_engine.db.search_by_word(token)
        print(f"\n搜索token '{token}' 找到 {len(results)} 个结果")
        
        # 查看前10个结果
        for i, result in enumerate(results[:10], 1):
            match_type = "ID" if result['word_type'] == 'id' else "DESC"
            print(f"  {i:2d}. [{match_type}] {result['field_id']} - {result['description'][:30]}...")
        
        all_results.extend(results)
    
    print(f"\n总共收集到 {len(all_results)} 个原始结果")
    
    # 模拟去重和排序过程
    unique_results = {}
    for result in all_results:
        key = (result['field_id'], result['region'], result['file_source'])
        if key not in unique_results:
            unique_results[key] = result
            unique_results[key]['match_count'] = 1
            unique_results[key]['matched_words'] = [result['word']]
            unique_results[key]['id_matches'] = 1 if result['word_type'] == 'id' else 0
            unique_results[key]['desc_matches'] = 1 if result['word_type'] == 'description' else 0
        else:
            unique_results[key]['match_count'] += 1
            if result['word'] not in unique_results[key]['matched_words']:
                unique_results[key]['matched_words'].append(result['word'])
            if result['word_type'] == 'id':
                unique_results[key]['id_matches'] += 1
            else:
                unique_results[key]['desc_matches'] += 1
    
    print(f"去重后有 {len(unique_results)} 个唯一结果")
    
    # 排序
    sorted_results = sorted(unique_results.values(), 
                          key=lambda x: (x['id_matches'], x['match_count'], len(x['matched_words'])), 
                          reverse=True)
    
    print(f"\n排序后前10个结果:")
    for i, result in enumerate(sorted_results[:10], 1):
        print(f"{i:2d}. {result['field_id']} - ID匹配:{result['id_matches']} - 总匹配:{result['match_count']}")
    
    # 查找volume字段的位置
    volume_positions = []
    for i, result in enumerate(sorted_results):
        if result['field_id'] == 'volume':
            volume_positions.append((i + 1, result))
    
    print(f"\nID为'volume'的字段位置:")
    for pos, result in volume_positions:
        print(f"  位置 {pos}: ID匹配={result['id_matches']}, 总匹配={result['match_count']}, 匹配词汇={result['matched_words']}")


if __name__ == "__main__":
    print("开始分析volume字段排名...")
    
    # 1. 分析volume排名
    analyze_volume_ranking()
    
    # 2. 检查排序逻辑
    check_sorting_logic()
    
    # 3. 检查搜索引擎逻辑
    check_search_engine_logic()
    
    print("\n分析完成！")
