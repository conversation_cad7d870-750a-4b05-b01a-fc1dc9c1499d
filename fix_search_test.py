# -*- coding: utf-8 -*-
"""
修复搜索测试 - 验证真正的问题
"""

from database import IndexDatabase
from tokenizer import EconomicsTokenizer
from app import SearchEngine


def test_database_search_without_limit():
    """测试数据库搜索不带限制"""
    print("=== 测试数据库搜索不带限制 ===")
    
    db = IndexDatabase()
    
    # 测试不带限制的搜索
    print("搜索'volume'不带限制:")
    results = db.search_by_word('volume')  # 不传入limit参数
    
    print(f"总结果: {len(results)}")
    
    # 分类统计
    id_results = [r for r in results if r['word_type'] == 'id']
    desc_results = [r for r in results if r['word_type'] == 'description']
    
    print(f"ID匹配: {len(id_results)} 个")
    print(f"描述匹配: {len(desc_results)} 个")
    
    # 显示ID匹配的结果
    print("\n前10个ID匹配结果:")
    for i, result in enumerate(id_results[:10], 1):
        print(f"{i}. {result['field_id']} - {result['description'][:50]}...")
    
    # 检查是否包含ID为volume的字段
    volume_id_results = [r for r in id_results if r['field_id'] == 'volume']
    print(f"\nID为'volume'的结果: {len(volume_id_results)} 个")
    
    for result in volume_id_results:
        print(f"  字段ID: {result['field_id']}")
        print(f"  描述: {result['description']}")
        print(f"  地区: {result['region']}")
        print(f"  文件: {result['file_source']}")


def test_search_engine_detailed():
    """详细测试搜索引擎"""
    print("\n=== 详细测试搜索引擎 ===")
    
    search_engine = SearchEngine()
    
    # 搜索volume
    results = search_engine.search("volume")
    
    print(f"搜索引擎找到 {len(results)} 个结果")
    
    # 查找ID为volume的结果
    volume_id_results = [r for r in results if r['field_id'] == 'volume']
    print(f"ID为'volume'的结果: {len(volume_id_results)} 个")
    
    if volume_id_results:
        print("\nID为'volume'的结果详情:")
        for i, result in enumerate(volume_id_results, 1):
            print(f"{i}. 字段ID: {result['field_id']}")
            print(f"   描述: {result['description']}")
            print(f"   地区: {result['region']}")
            print(f"   文件: {result['file_source']}")
            print(f"   ID匹配: {result.get('id_matches', 0)}")
            print(f"   描述匹配: {result.get('desc_matches', 0)}")
            print(f"   匹配词汇: {result.get('matched_words', [])}")
            print()
    
    # 检查前10个结果中是否有ID为volume的
    print("前10个结果中ID为'volume'的:")
    top_volume_results = [r for r in results[:10] if r['field_id'] == 'volume']
    print(f"数量: {len(top_volume_results)}")


def test_exact_volume_search():
    """测试精确的volume搜索"""
    print("\n=== 测试精确的volume搜索 ===")
    
    db = IndexDatabase()
    
    with db.get_connection() as conn:
        # 查找ID类型的volume索引，按排序规则排序
        cursor = conn.execute('''
            SELECT DISTINCT f.*, wi.word, wi.word_type, wi.source_text
            FROM fields f
            JOIN word_index wi ON f.field_id = wi.field_id
            WHERE wi.word = 'volume'
            ORDER BY wi.word_type ASC, f.category_name, f.field_id
            LIMIT 20
        ''')
        
        results = cursor.fetchall()
        print(f"按排序规则的前20个结果:")
        
        for i, row in enumerate(results, 1):
            print(f"{i}. 字段ID: {row[1]} ({row[15]})")  # field_id, word_type
            print(f"   描述: {row[2][:50]}...")  # description
            print(f"   匹配类型: {row[15]}")  # word_type
            print()


def compare_search_methods():
    """比较不同搜索方法的结果"""
    print("\n=== 比较不同搜索方法 ===")
    
    db = IndexDatabase()
    search_engine = SearchEngine()
    
    # 方法1: 直接数据库搜索
    db_results = db.search_by_word('volume')
    volume_db_results = [r for r in db_results if r['field_id'] == 'volume']
    
    # 方法2: 搜索引擎搜索
    engine_results = search_engine.search("volume")
    volume_engine_results = [r for r in engine_results if r['field_id'] == 'volume']
    
    print(f"数据库搜索找到ID为'volume'的结果: {len(volume_db_results)} 个")
    print(f"搜索引擎找到ID为'volume'的结果: {len(volume_engine_results)} 个")
    
    # 检查排序位置
    if volume_engine_results:
        for result in volume_engine_results:
            position = next((i for i, r in enumerate(engine_results) if r['field_id'] == result['field_id'] and r['region'] == result['region']), -1)
            print(f"ID为'volume'的字段在搜索结果中的位置: {position + 1}")


if __name__ == "__main__":
    print("开始修复搜索测试...")
    
    # 1. 测试数据库搜索不带限制
    test_database_search_without_limit()
    
    # 2. 详细测试搜索引擎
    test_search_engine_detailed()
    
    # 3. 测试精确搜索
    test_exact_volume_search()
    
    # 4. 比较搜索方法
    compare_search_methods()
    
    print("\n测试完成！")
