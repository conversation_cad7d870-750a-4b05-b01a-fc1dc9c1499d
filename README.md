# 经济学数据字段双语模糊搜索引擎

这是一个专为经济学数据字段设计的智能搜索引擎，支持中英文双语查询和模糊匹配。

## 🚀 功能特性

- **双语支持**: 同时支持中文和英文搜索查询
- **智能分词**: 使用jieba分词器处理中文，自定义经济学专业词汇库
- **模糊搜索**: 基于词汇索引的智能模糊匹配
- **分类搜索**: 支持按数据类别（Price Volume、Fundamental、Model等）筛选
- **地区筛选**: 支持按地区（USA、CHN、EUR等）筛选
- **Web界面**: 提供友好的Web搜索界面
- **API接口**: 提供RESTful API供其他应用调用

## 📊 数据覆盖

- **总字段数**: 420,000+ 个经济学数据字段
- **数据类别**: 
  - Price Volume (价格成交量)
  - Fundamental (基本面)
  - Model (模型)
  - News (新闻)
  - Sentiment (情绪)
  - Analyst (分析师)
  - 等等...
- **覆盖地区**: USA、CHN、EUR、GLB、ASI

## 🛠️ 技术架构

### 核心组件

1. **分词系统** (`tokenizer.py`)
   - ID字段分词：基于下划线分割
   - 描述分词：中英文混合文本智能分词
   - 经济学专业词汇库

2. **索引数据库** (`database.py`)
   - SQLite数据库存储
   - 词汇到字段的映射关系
   - 高效的查询索引

3. **数据处理** (`data_processor.py`)
   - CSV文件批量处理
   - 数据清洗和标准化
   - 索引构建

4. **搜索引擎** (`app.py`)
   - 智能搜索算法
   - 相关性排序
   - Web API接口

## 📦 安装和使用

### 环境要求

- Python 3.8+
- 依赖包见 `requirements.txt`

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd 数据字段检索器
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **构建索引**
```bash
# 测试版本（处理前3个文件）
python -c "
from data_processor import DataProcessor
processor = DataProcessor()
csv_files = processor.get_csv_files()[:3]
for file_path in csv_files:
    processor.process_csv_file(file_path)
processor.db.update_category_stats()
"

# 完整版本（处理所有文件）
python build_index.py
```

4. **启动Web服务**
```bash
python app.py
```

5. **访问应用**
打开浏览器访问 `http://localhost:5000`

## 🔍 使用示例

### Web界面搜索

1. 打开 `http://localhost:5000`
2. 在搜索框输入关键词，如：
   - `market cap` (市值)
   - `volume` (成交量)
   - `beta` (贝塔系数)
   - `price ratio` (价格比率)
3. 可选择类别和地区进行筛选
4. 查看搜索结果

### API调用

```bash
# 基本搜索
curl "http://localhost:5000/api/search?q=market%20cap"

# 按类别搜索
curl "http://localhost:5000/api/search?q=volume&category=Price%20Volume"

# 按地区搜索
curl "http://localhost:5000/api/search?q=beta&region=USA"

# 获取统计信息
curl "http://localhost:5000/api/stats"
```

### 命令行测试

```bash
# 测试分词功能
python test_tokenizer.py

# 测试搜索功能
python test_search.py
```

## 📁 项目结构

```
数据字段检索器/
├── split_files/           # CSV数据文件目录
├── data/                  # 数据库和词典文件
├── templates/             # Web模板
├── config.py             # 配置文件
├── database.py           # 数据库操作
├── tokenizer.py          # 分词处理
├── data_processor.py     # 数据处理
├── app.py               # Web应用
├── build_index.py       # 索引构建脚本
├── test_tokenizer.py    # 分词测试
├── test_search.py       # 搜索测试
├── requirements.txt     # 依赖包
└── README.md           # 项目说明
```

## 🎯 搜索示例

### 常用经济学术语搜索

- **基本面指标**: `assets`, `liability`, `equity`, `revenue`, `profit`
- **技术指标**: `moving average`, `volume`, `price`, `volatility`
- **估值指标**: `P/E ratio`, `P/B ratio`, `market cap`, `enterprise value`
- **风险指标**: `beta`, `alpha`, `sharpe ratio`, `VaR`

### 中文搜索

- `市值`, `成交量`, `市盈率`, `净资产收益率`
- `移动平均`, `波动率`, `贝塔系数`

## 🔧 配置说明

主要配置在 `config.py` 中：

- `DATA_CONFIG`: 数据文件路径配置
- `TOKENIZER_CONFIG`: 分词器配置
- `SEARCH_CONFIG`: 搜索参数配置
- `ECONOMICS_TERMS`: 经济学专业词汇库

## 📈 性能指标

- **索引构建速度**: ~1000 记录/秒
- **搜索响应时间**: <100ms
- **内存使用**: ~200MB (包含完整索引)
- **存储空间**: ~50MB (SQLite数据库)

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 这是第一阶段的实现，主要完成了分词和索引构建功能。后续将继续开发更高级的搜索功能和用户界面。
