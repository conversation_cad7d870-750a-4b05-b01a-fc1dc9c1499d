# -*- coding: utf-8 -*-
"""
调试ID搜索问题的脚本
"""

from database import IndexDatabase
from tokenizer import EconomicsTokenizer
import sqlite3


def debug_id_tokenization():
    """调试ID分词功能"""
    print("=== 调试ID分词功能 ===")
    
    tokenizer = EconomicsTokenizer()
    
    # 测试包含volume的ID
    test_ids = [
        'volume',
        'adv20',  # Average daily volume
        'mdl175_01amev',  # volume相关
        'pv27_s_dq_volume',  # 直接包含volume
        'fnd5_volu',  # volume缩写
        'mdl23_bk_average_daily_vol'  # volume缩写
    ]
    
    print("测试ID分词结果：")
    for field_id in test_ids:
        tokens = tokenizer.tokenize_field_id(field_id)
        print(f"ID: {field_id}")
        print(f"分词结果: {tokens}")
        print(f"是否包含'volume'相关词: {'volume' in ' '.join(tokens).lower() or 'vol' in ' '.join(tokens).lower()}")
        print("-" * 50)


def check_database_id_index():
    """检查数据库中ID索引的情况"""
    print("\n=== 检查数据库ID索引 ===")
    
    db = IndexDatabase()
    
    with db.get_connection() as conn:
        # 查找包含volume的ID词汇索引
        print("1. 查找包含'volume'的ID词汇索引：")
        cursor = conn.execute('''
            SELECT word, field_id, source_text, COUNT(*) as count
            FROM word_index 
            WHERE word_type = 'id' AND (word LIKE '%volume%' OR word LIKE '%vol%')
            GROUP BY word, field_id
            ORDER BY word, field_id
            LIMIT 20
        ''')
        
        results = cursor.fetchall()
        if results:
            for row in results:
                print(f"  词汇: {row[0]}, 字段ID: {row[1]}, 原文: {row[2]}, 计数: {row[3]}")
        else:
            print("  未找到包含'volume'的ID词汇索引")
        
        # 查找所有ID类型的词汇统计
        print("\n2. ID类型词汇统计：")
        cursor = conn.execute('''
            SELECT COUNT(DISTINCT word) as unique_words, COUNT(*) as total_entries
            FROM word_index 
            WHERE word_type = 'id'
        ''')
        stats = cursor.fetchone()
        print(f"  唯一ID词汇数: {stats[0]}, 总ID索引条目: {stats[1]}")
        
        # 查找包含volume的字段ID
        print("\n3. 查找包含'volume'的字段ID：")
        cursor = conn.execute('''
            SELECT field_id, description, file_source
            FROM fields 
            WHERE field_id LIKE '%volume%' OR field_id LIKE '%vol%'
            ORDER BY field_id
            LIMIT 10
        ''')
        
        field_results = cursor.fetchall()
        if field_results:
            for row in field_results:
                print(f"  字段ID: {row[0]}")
                print(f"  描述: {row[1]}")
                print(f"  文件: {row[2]}")
                print()
        else:
            print("  未找到包含'volume'的字段ID")


def test_search_for_volume():
    """测试搜索volume的结果"""
    print("\n=== 测试搜索'volume'的结果 ===")
    
    db = IndexDatabase()
    
    # 直接搜索volume
    results = db.search_by_word('volume', limit=10)
    print(f"搜索'volume'找到 {len(results)} 个结果：")
    
    for i, result in enumerate(results, 1):
        print(f"{i}. 字段ID: {result['field_id']}")
        print(f"   描述: {result['description']}")
        print(f"   匹配词汇: {result['word']}")
        print(f"   词汇类型: {result['word_type']}")
        print(f"   原文: {result['source_text']}")
        print()


def test_specific_volume_ids():
    """测试特定包含volume的ID"""
    print("\n=== 测试特定包含volume的ID ===")
    
    db = IndexDatabase()
    tokenizer = EconomicsTokenizer()
    
    # 从数据库中找一些实际的包含volume的字段
    with db.get_connection() as conn:
        cursor = conn.execute('''
            SELECT field_id, description 
            FROM fields 
            WHERE description LIKE '%volume%' 
            ORDER BY field_id 
            LIMIT 5
        ''')
        
        volume_fields = cursor.fetchall()
        
        print("找到的包含volume描述的字段：")
        for field_id, description in volume_fields:
            print(f"\n字段ID: {field_id}")
            print(f"描述: {description}")
            
            # 测试ID分词
            id_tokens = tokenizer.tokenize_field_id(field_id)
            print(f"ID分词: {id_tokens}")
            
            # 测试描述分词
            desc_tokens = tokenizer.tokenize_description(description)
            print(f"描述分词: {desc_tokens}")
            
            # 检查索引中是否存在
            cursor2 = conn.execute('''
                SELECT word, word_type, source_text
                FROM word_index 
                WHERE field_id = ?
                ORDER BY word_type, word
            ''', (field_id,))
            
            index_results = cursor2.fetchall()
            print(f"索引中的词汇 ({len(index_results)} 个):")
            for word, word_type, source_text in index_results:
                print(f"  {word_type}: {word} (来源: {source_text})")


def check_word_index_integrity():
    """检查词汇索引的完整性"""
    print("\n=== 检查词汇索引完整性 ===")
    
    db = IndexDatabase()
    
    with db.get_connection() as conn:
        # 检查是否有字段没有对应的索引
        cursor = conn.execute('''
            SELECT f.field_id, f.description, COUNT(wi.word) as word_count
            FROM fields f
            LEFT JOIN word_index wi ON f.field_id = wi.field_id
            GROUP BY f.field_id, f.description
            HAVING word_count = 0
            LIMIT 5
        ''')
        
        no_index_fields = cursor.fetchall()
        if no_index_fields:
            print("发现没有索引的字段：")
            for field_id, description, count in no_index_fields:
                print(f"  {field_id}: {description}")
        else:
            print("所有字段都有对应的索引")
        
        # 检查索引统计
        cursor = conn.execute('''
            SELECT 
                word_type,
                COUNT(DISTINCT word) as unique_words,
                COUNT(*) as total_entries,
                COUNT(DISTINCT field_id) as unique_fields
            FROM word_index
            GROUP BY word_type
        ''')
        
        print("\n索引统计：")
        for word_type, unique_words, total_entries, unique_fields in cursor.fetchall():
            print(f"  {word_type}: {unique_words} 个唯一词汇, {total_entries} 个索引条目, {unique_fields} 个字段")


if __name__ == "__main__":
    print("开始调试ID搜索问题...")
    
    # 1. 调试ID分词
    debug_id_tokenization()
    
    # 2. 检查数据库索引
    check_database_id_index()
    
    # 3. 测试搜索
    test_search_for_volume()
    
    # 4. 测试特定ID
    test_specific_volume_ids()
    
    # 5. 检查索引完整性
    check_word_index_integrity()
    
    print("\n调试完成！")
