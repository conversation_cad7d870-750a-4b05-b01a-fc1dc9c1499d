# -*- coding: utf-8 -*-
"""
分词功能测试脚本
"""

from tokenizer import EconomicsTokenizer
from data_processor import DataProcessor
import json


def test_id_tokenization():
    """测试ID分词功能"""
    print("=== 测试ID分词功能 ===")
    
    tokenizer = EconomicsTokenizer()
    
    test_ids = [
        'adv20',
        'fnd27_acct_payable',
        'mdl175_01am',
        'pv27_s_dq_adjopen',
        'snt27_avgranking_100',
        'sta1_top2000c10'
    ]
    
    for field_id in test_ids:
        tokens = tokenizer.tokenize_field_id(field_id)
        print(f"ID: {field_id}")
        print(f"分词结果: {tokens}")
        print("-" * 40)


def test_description_tokenization():
    """测试描述分词功能"""
    print("\n=== 测试描述分词功能 ===")
    
    tokenizer = EconomicsTokenizer()
    
    test_descriptions = [
        "Average daily volume in past 20 days",
        "Daily market capitalization (in millions)",
        "accounts payable",
        "Advance payment",
        "Capital accumulation fund",
        "Share capital",
        "Construction in progress",
        "Deferred income - non-current liabilities",
        "Available for sale financial assets",
        "Price-to-book ratio",
        "Return on assets",
        "Beta (24 months). It computes 24 months beta using the CAPM model.",
        "It refers to the average stock's price",
        "Turnover rate (benchmark, free float equity). It describes the liquidity of stock."
    ]
    
    for desc in test_descriptions:
        tokens = tokenizer.tokenize_description(desc)
        print(f"描述: {desc}")
        print(f"分词结果: {tokens}")
        print("-" * 60)


def test_combined_tokenization():
    """测试完整字段分词"""
    print("\n=== 测试完整字段分词 ===")
    
    tokenizer = EconomicsTokenizer()
    
    test_fields = [
        ('adv20', 'Average daily volume in past 20 days'),
        ('fnd27_acct_payable', 'accounts payable'),
        ('mdl175_bp', 'Price-to-book ratio'),
        ('pv27_s_dq_freeturnover', 'Turnover rate (benchmark, free float equity). It describes the liquidity of stock.')
    ]
    
    for field_id, description in test_fields:
        result = tokenizer.tokenize_field(field_id, description)
        print(f"字段ID: {field_id}")
        print(f"描述: {description}")
        print(f"ID分词: {result['id_tokens']}")
        print(f"描述分词: {result['description_tokens']}")
        print("=" * 80)


def test_with_real_data():
    """使用真实数据测试"""
    print("\n=== 使用真实数据测试 ===")
    
    processor = DataProcessor()
    sample_data = processor.get_sample_data(5)
    
    if not sample_data:
        print("未找到样本数据")
        return
    
    for i, field_data in enumerate(sample_data, 1):
        print(f"\n--- 样本 {i} ---")
        print(f"字段ID: {field_data['field_id']}")
        print(f"描述: {field_data['description']}")
        print(f"类别: {field_data['category_name']}")
        print(f"地区: {field_data['region']}")
        print(f"ID分词: {field_data['tokens']['id_tokens']}")
        print(f"描述分词: {field_data['tokens']['description_tokens']}")


def test_data_distribution():
    """测试数据分布分析"""
    print("\n=== 数据分布分析 ===")
    
    processor = DataProcessor()
    distribution = processor.analyze_data_distribution()
    
    print(f"总字段数: {distribution['total_fields']}")
    print(f"文件数: {len(distribution['files'])}")
    
    print("\n类别分布:")
    for category, count in sorted(distribution['categories'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {category}: {count}")
    
    print("\n地区分布:")
    for region, count in sorted(distribution['regions'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {region}: {count}")
    
    print("\n文件详情:")
    for file_info in distribution['files'][:5]:  # 只显示前5个文件
        print(f"  {file_info['file']}: {file_info['records']} 条记录")


if __name__ == "__main__":
    print("开始测试分词功能...")
    
    # 测试ID分词
    test_id_tokenization()
    
    # 测试描述分词
    test_description_tokenization()
    
    # 测试完整分词
    test_combined_tokenization()
    
    # 测试真实数据
    test_with_real_data()
    
    # 测试数据分布
    test_data_distribution()
    
    print("\n测试完成！")
